<%# Ad<PERSON> Page - using main layout %>
<div class="px-6 flex flex-1 justify-center py-5">
    <div class="layout-content-container flex flex-col w-full max-w-none flex-1">
        <%# Page header %>
        <div class="flex flex-wrap justify-between items-center gap-3 p-4">
            <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">Membros</p>
                <p class="text-[#637688] text-sm font-normal leading-normal">
                    Gerencie os membros e suas atribuições.
                </p>
            </div>
        </div>

        <%# Search bar %>
        <div class="px-4 py-3">
            <label class="flex flex-col min-w-40 h-12 w-full">
                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                    <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                        data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                            fill="currentColor" viewBox="0 0 256 256">
                            <path
                                d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                            </path>
                        </svg>
                    </div>
                    <input placeholder="Buscar membros..." id="searchMembros"
                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-full placeholder:text-[#637688] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                        value="">
                </div>
            </label>
        </div>
        <%# Members table %>
        <div class="px-4 py-3 @container">
            <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                <table class="flex-1">
                    <thead>
                        <tr class="bg-white">
                            <th class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                Nome
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                Cargo
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                Inspeções Atribuídas
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                Disponibilidade
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <%# Dynamic member rows %>
                        <%
                        const defaultMembers = [
                            {
                                id: 1,
                                nome: 'Dra. Mariana Silva',
                                cargo: 'Inspetora Líder',
                                inspecoesAtribuidas: ['INSP-001'],
                                disponibilidade: 'Disponível'
                            },
                            {
                                id: 2,
                                nome: 'Eng. Carlos Santos',
                                cargo: 'Engenheiro Estrutural',
                                inspecoesAtribuidas: ['INSP-002', 'INSP-003'],
                                disponibilidade: 'Ocupado'
                            },
                            {
                                id: 3,
                                nome: 'Sra. Ana Pereira',
                                cargo: 'Especialista Ambiental',
                                inspecoesAtribuidas: [],
                                disponibilidade: 'Disponível'
                            },
                            {
                                id: 4,
                                nome: 'Sr. João Almeida',
                                cargo: 'Oficial de Segurança',
                                inspecoesAtribuidas: [],
                                disponibilidade: 'Disponível'
                            }
                        ];

                        const members = typeof membersData !== 'undefined' && membersData.members ? membersData.members : defaultMembers;
                        %>

                        <% members.forEach(function(member) { %>
                            <tr class="border-t border-t-[#dce1e5]">
                                <td class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= member.nome %>
                                </td>
                                <td class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= member.cargo %>
                                </td>
                                <td class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                    <% if (member.inspecoesAtribuidas && member.inspecoesAtribuidas.length > 0) { %>
                                        <%= member.inspecoesAtribuidas.join(', ') %>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                    <%
                                    let statusClass = '';
                                    switch(member.disponibilidade) {
                                        case 'Disponível':
                                            statusClass = 'bg-[#f6ffed] text-[#52c41a]';
                                            break;
                                        case 'Ocupado':
                                            statusClass = 'bg-[#fff1f0] text-[#f5222d]';
                                            break;
                                        default:
                                            statusClass = 'bg-[#f5f5f5] text-[#8c8c8c]';
                                    }
                                    %>
                                    <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 <%= statusClass %> text-sm font-medium leading-normal w-full mx-auto">
                                        <span class="truncate"><%= member.disponibilidade %></span>
                                    </button>
                                </td>
                                <td class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                    <a href="/admin/detalhes-membro?id=<%= member.id %>" class="text-blue-600 hover:underline">
                                        Ver Detalhes
                                    </a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<%# Members page JavaScript %>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = document.getElementById('searchMembros');
        const tableRows = document.querySelectorAll('tbody tr');

        <%# Search functionality %>
        function filterMembers() {
            const searchTerm = searchInput.value.toLowerCase();

            tableRows.forEach(row => {
                const nomeText = row.cells[0].textContent.toLowerCase();
                const cargoText = row.cells[1].textContent.toLowerCase();

                const matches = nomeText.includes(searchTerm) || cargoText.includes(searchTerm);

                if (matches) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        <%# Attach event listener %>
        searchInput.addEventListener('input', filterMembers);
    });
</script>
const db = require('../config/db');

class patologias
 {
  static async getAllPatologia() {
    const result = await db.query('SELECT * FROM patologias');
    return result.rows;
  }

  static async getPatologiaById(id) {
    const result = await db.query('SELECT * FROM patologias WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createPatologia(data) {
    const result = await db.query(
      'INSERT INTO patologias (sistema_id, nome, descricao, criado_por, criado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [data.sistema_id, data.nome, data.descricao, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async updatePatologia(data) {
    const result = await db.query(
      'UPDATE patologias SET sistema_id  =$1, nome  =$2, descricao =$3, criado_por =$4, criado_em =$5, atualizado_em=$6, WHERE id =$7 RETURNING *',
      [data.sistema_id, data.nome, data.descricao, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async deletePatologia(id) {
    const result = await db.query('DELETE FROM patologias WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = patologias;

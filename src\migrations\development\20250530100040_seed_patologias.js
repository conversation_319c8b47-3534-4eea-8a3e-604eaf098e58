const db = require('../../config/db');

module.exports = {
  up: async () => {
    await db.query(`
      CREATE TABLE IF NOT EXISTS patologias (
        id SERIAL PRIMARY KEY,
        sistema_id INTEGER,
        nome VARCHAR(100),
        descricao TEXT,
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (sistema_id) REFERENCES sistemas(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS patologias CASCADE;');
  },
};

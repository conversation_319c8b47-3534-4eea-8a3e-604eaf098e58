const db = require('../config/db');

class fotos
 {
  static async getAllFoto() {
    const result = await db.query('SELECT * FROM fotos');
    return result.rows;
  }

  static async getFotoById(id) {
    const result = await db.query('SELECT * FROM fotos WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createFoto(data) {
    const result = await db.query(
      'INSERT INTO fotos (patologia_id, caminho_arquivo, legenda, criado_por, criado_em) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [data.patologia_id, data.caminho_arquivo, data.legenda, data.criado_por, data.criado_em]
    );
    return result.rows[0];
  }

  static async updateFoto(data) {
    const result = await db.query(
      'UPDATE fotos SET patologia_id  =$1, caminho_arquivo  =$2, legenda  =$3, criado_por  =$4, criado_em  =$5 RETURNING *',
      [data.patologia_id, data.caminho_arquivo, data.legenda, data.criado_por, data.criado_em]
    );
    return result.rows[0];
  }

  static async deleteFoto(id) {
    const result = await db.query('DELETE FROM fotos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = fotos;

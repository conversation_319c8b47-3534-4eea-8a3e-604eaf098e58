const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS sistemas (
      id SERIAL PRIMARY KEY,
      ambiente_id INTEGER,
      tipo VARCHAR(100), -- piso, parede, teto
      descricao TEXT,
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (ambiente_id) REFERENCES ambientes(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "sistemas" criada com sucesso.');
};

const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS funcoes_usuarios (
      id SERIAL PRIMARY KEY,
      usuario_id INTEGER,
      funcao VARCHAR(50), -- inspetor, coordenador, administrador
      criado_em TIMESTAMP,
      FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "funcoes_usuarios" criada com sucesso.');
};
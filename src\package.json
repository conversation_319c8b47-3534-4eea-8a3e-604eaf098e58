{"name": "2025-1b-t16-in02-g01", "version": "1.0.0", "description": "<p align=\"center\">\r <a href= \"https://www.inteli.edu.br/\"><img src=\"/assets/inteli.png\" alt=\"Inteli - Instituto de Tecnologia e Liderança\" border=\"0\"></a>\r </p>", "main": "server.js", "dependencies": {"cross-env": "^7.0.3", "cross-spawn": "^7.0.6", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "isexe": "^2.0.0", "path-key": "^3.1.1", "pg": "^8.16.0", "pg-cloudflare": "^1.2.5", "pg-connection-string": "^2.9.0", "pg-int8": "^1.0.1", "pg-pool": "^3.10.0", "pg-protocol": "^1.10.0", "pg-types": "^2.2.0", "pgpass": "^1.0.5", "postgres-array": "^2.0.0", "postgres-bytea": "^1.0.0", "postgres-date": "^1.0.7", "postgres-interval": "^1.2.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "split2": "^4.2.0", "which": "^2.0.2", "xtend": "^4.0.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:coverage": "jest --coverage", "init-db": "node scripts/runSQLScript.js", "migrations:dev": "node migrations/runMigrations.js development", "migrations:prod": "node migrations/runMigrations.js production", "migrations": "npm run migrations:prod && npm run migrations:dev"}, "repository": {"type": "git", "url": "git+https://github.com/Inteli-College/2025-1B-T16-IN02-G01.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Inteli-College/2025-1B-T16-IN02-G01/issues"}, "homepage": "https://github.com/Inteli-College/2025-1B-T16-IN02-G01#readme"}
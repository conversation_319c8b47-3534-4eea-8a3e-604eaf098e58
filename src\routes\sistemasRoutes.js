const express = require('express');
const router = express.Router();
const sistemasControllers = require('../controllers/sistemasControllers');

router.get('/', sistemasControllers.getAllSistema);
router.get('/:id', sistemasControllers.getSistemaById);
router.post('/', sistemasControllers.createSistema);
router.put('/:id', sistemasControllers.updateSistema);
router.delete('/:id', sistemasControllers.deleteSistema);

module.exports = router;

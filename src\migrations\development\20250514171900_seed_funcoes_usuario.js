const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async (pool) => {
    const sqlPath = path.join(__dirname, 'seed_funcoes_usuario.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await pool.query(sql);
    console.log('Dados inseridos em "funções usuário" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS funcoes_usuarios CASCADE;');
  },
};
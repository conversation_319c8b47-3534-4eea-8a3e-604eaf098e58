const db = require('../config/db');

class pavimentos {
  static async getAllPavimento() {
    const result = await db.query('SELECT * FROM pavimentos');
    return result.rows;
  }

  static async getPavimentoById(id) {
    const result = await db.query('SELECT * FROM pavimentos WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createPavimento(data) {
    const result = await db.query(
      'INSERT INTO pavimentos (edificio_id, andar, criado_por, criado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [data.edificio_id, data.andar, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async updatePavimento(data) {
    const result = await db.query(
      'UPDATE pavimentos SET edificio_id =$1, andar =$2, criado_por =$3, criado_em =$4, atualizado_em =$5 WHERE id = $6 RETURNING *',
      [data.edificio_id, data.andar, data.criado_por, data.criado_em, data.atualizado_em, data.id]
    );
    return result.rows[0];
  }

  static async deletePavimento(id) {
    const result = await db.query('DELETE FROM pavimentos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = pavimentos;

<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= typeof pageTitle !== 'undefined' ? pageTitle : 'InfraWatch' %></title>

  <%# CSS Dependencies %>
  <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
  <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
    href="https://fonts.googleapis.com/css2?display=swap&family=Noto+Sans:wght@400;500;700;900&family=Public+Sans:wght@400;500;700;900">
  <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,">
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

  <%# Additional CSS for specific pages %>
  <% if (typeof additionalCSS !== 'undefined') { %>
    <%- additionalCSS %>
  <% } %>

  <style>
    .nav-item-active {
      transform: scale(1.1);
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
      background-color: #eff6ff;
    }
    [x-cloak] { display: none !important; }
  </style>
</head>

<body>
  <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
    style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
    <div class="layout-container flex h-full grow flex-col">

      <%# Include header component with user role %>
      <%- include('../components/header', {
        userRole: typeof userRole !== 'undefined' ? userRole : null,
        pageTitle: typeof pageTitle !== 'undefined' ? pageTitle : 'InfraWatch',
        currentPage: typeof currentPage !== 'undefined' ? currentPage : ''
      }) %>

      <%# Main content area %>
      <main class="flex-1">
        <%- body %>
      </main>

      <%# Include footer component %>
      <%- include('../components/footer') %>
    </div>
  </div>

  <%# JavaScript Dependencies %>
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <%# Additional JavaScript for specific pages %>
  <% if (typeof additionalJS !== 'undefined') { %>
    <%- additionalJS %>
  <% } %>
</body>

</html>
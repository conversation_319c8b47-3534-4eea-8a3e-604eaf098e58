const db = require('../config/db');

class relatorios {
  static async getAll() {
    const result = await db.query('SELECT * FROM relatorios');
    return result.rows;
  }

  static async getById(id) {
    const result = await db.query('SELECT * FROM relatorios WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(data) {
    const result = await db.query(
      'INSERT INTO relatorios (inspecao_id, titulo, status, gerado_por, gerado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [data.inspecao_id, data.titulo, data.status, data.gerado_por, data.gerado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async update(id, data) {
    const result = await db.query(
      'UPDATE relatorios SET inspecao_id = $1, titulo = $2, status = $3, gerado_por = $4, gerado_em = $5, atualizado_em = $6 WHERE id = $7 RETURNING *',
      [data.inspecao_id, data.titulo, data.status, data.gerado_por, data.gerado_em, data.atualizado_em, id]
    );
    return result.rows[0];
  }

  static async delete(id) {
    const result = await db.query('DELETE FROM relatorios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = relatorios;

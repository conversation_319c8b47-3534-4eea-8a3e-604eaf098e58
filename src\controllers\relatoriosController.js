const RelatoriosModel = require('../models/relatoriosModel');

const getAllRelatorios = async (req, res) => {
  try {
    const relatorios = await RelatorioService.getAllRelatorios();
    res.status(200).json(relatorios);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getRelatoriosById = async (req, res) => {
  try {
    const relatorios = await RelatoriosService.getRelatoriosById(req.params.id);
    if (relatorios) {
      res.status(200).json(relatorios);
    } else {
      res.status(404).json({ error: 'Relatório não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createRelatorios = async (req, res) => {
  try {
    const { inspecao_id, titulo, status, gerado_por, gerado_em, atualizado_em } = req.body;
    const newRelatorios = await RelatoriosService.createRelatorios(inspecao_id, titulo, status, gerado_por, gerado_em, atualizado_em);
    res.status(201).json(newRelatorios);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateRelatorios = async (req, res) => {
  try {
    const { inspecao_id, titulo, status, gerado_por, gerado_em, atualizado_em } = req.body;
    const updatedRelatorios = await RelatoriosService.updateRelatorios(
      req.params.id,
      inspecao_id,
      titulo,
      status,
      gerado_por,
      gerado_em,
      atualizado_em
    );
    if (updatedRelatorios) {
      res.status(200).json(updatedRelatorios);
    } else {
      res.status(404).json({ error: 'Relatório não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteRelatorios = async (req, res) => {
  try {
    const deletedRelatorios = await RelatoriosService.deleteRelatorios(req.params.id);
    if (deletedRelatorios) {
      res.status(200).json(deletedRelatorios);
    } else {
      res.status(404).json({ error: 'Relatório não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAllRelatorios,
  getRelatoriosById,
  createRelatorios,
  updateRelatorios,
  deleteRelatorios
};

const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS patologias (
      id SERIAL PRIMARY KEY,
      sistema_id INTEGER,
      nome VARCHAR(100),
      descricao TEXT,
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (sistema_id) REFERENCES sistemas(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "patologias" criada com sucesso.');
};

const db = require('../../config/db');

module.exports = {
  up: async () => {
    await db.query(`
      CREATE TABLE IF NOT EXISTS sistemas (
        id SERIAL PRIMARY KEY,
        ambiente_id INTEGER,
        tipo VARCHAR(100), -- piso, parede, teto
        descricao TEXT,
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (ambiente_id) REFERENCES ambientes(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS sistemas CASCADE;');
  },
};

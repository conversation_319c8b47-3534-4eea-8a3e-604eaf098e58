const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async () => {
    const sqlPath = path.join(__dirname, 'seed_relatorios.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await db.query(`
      CREATE TABLE IF NOT EXISTS relatorios (
        id SERIAL PRIMARY KEY,
        inspecao_id INTEGER,
        titulo VARCHAR(100),
        status VARCHAR(100), -- rascunho, final
        gerado_por INTEGER,
        gerado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
        FOREIGN KEY (gerado_por) REFERENCES usuarios(id)
      );
    `);
    await db.query(sql);
    console.log('Dados inseridos em "relatórios" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS relatorios CASCADE;');
  },
};

const db = require('../config/db');

class equipes_inspecao {
  static async getAll() {
    const result = await db.query('SELECT * FROM equipes_inspecoes');
    return result.rows;
  }

  static async getById(id) {
    const result = await db.query('SELECT * FROM equipes_inspecoes WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(data) {
    const result = await db.query(
      'INSERT INTO equipes_inspecoes (inspecao_id, usuario_id, funcao, criado_em) VALUES ($1, $2, $3, $4) RETURNING *',
      [data.inspecao_id, data.usuario_id, data.funcao, data.criado_em]
    );
    return result.rows[0];
  }

  static async update(id, data) {
    const result = await db.query(
      'UPDATE equipes_inspecoes SET inspecao_id = $1, usuario_id = $2, funcao = $3, criado_em = $4 WHERE id = $5 RETURNING *',
      [data.inspecao_id, data.usuario_id, data.funcao, data.criado_em, id]
    );
    return result.rows[0];
  }

  static async delete(id) {
    const result = await db.query('DELETE FROM equipes_inspecoes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = equipes_inspecao;


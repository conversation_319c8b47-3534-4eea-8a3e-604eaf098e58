// controllers/edificioControllers.js

const ambientesModel = require('../models/ambientesModel');

const getAllAmbiente = async (req, res) => {
  try {
    const ambiente = await ambientesModel.getAllAmbiente();
    res.status(200).json(ambiente);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getAmbienteById = async (req, res) => {
  try {
    const ambiente = await ambientesModel.getAmbienteById(req.params.id);
    if (ambiente) {
      res.status(200).json(ambiente);
    } else {
      res.status(404).json({ error: 'Ambiente não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createAmbiente = async (req, res) => {
  try {
    const {pavimento_id, nome, criado_por, criado_em, atualizado_em} = req.body;
    const newAmbiente = await ambientesModel.createAmbiente(pavimento_id, nome, criado_por, criado_em, atualizado_em);
    res.status(201).json(newAmbiente);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateAmbiente = async (req, res) => {
  try {
    const {pavimento_id, nome, criado_por, criado_em, atualizado_em} = req.body;
    const updatedAmbiente = await ambientesModel.updateAmbiente(req.params.id,pavimento_id, nome, criado_por, criado_em, atualizado_em);
    if (updatedAmbiente) {
      res.status(200).json(updatedAmbiente);
    } else {
      res.status(404).json({ error: 'Ambiente não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteAmbiente = async (req, res) => {
  try {
    const deletedAmbiente = await ambientesModel.deleteAmbiente(req.params.id);
    if (deletedAmbiente) {
      res.status(200).json(deletedAmbiente);
    } else {
      res.status(404).json({ error: 'Ambiente não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllAmbiente,
getAmbienteById,
createAmbiente,
updateAmbiente,
deleteAmbiente

};

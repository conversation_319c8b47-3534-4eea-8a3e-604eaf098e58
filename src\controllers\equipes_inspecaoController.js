const equipes_inspecaoModel = require('../models/equipes_inspecaoModel');

const getAllEquipes_inspecoes = async (req, res) => {
  try {
    const equipes_inspecoes = await equipes_inspecaoService.getAllEquipes_inspecoes();
    res.status(200).json(equipes_inspecoes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getEquipes_inspecaoById = async (req, res) => {
  try {
    const equipes_inspecao = await equipes_inspecaoService.getEquipes_inspecaoById(req.params.id);
    if (equipes_inspecao) {
      res.status(200).json(equipes_inspecao);
    } else {
      res.status(404).json({ error: 'Equipe não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createEquipes_inspecao = async (req, res) => {
  try {
    const { inspecao_id, usuario_id, funcao, criado_em } = req.body;
    const newEquipes_inspecao = await equipes_inspecaoService.createEquipes_inspecao(inspecao_id, usuario_id, funcao, criado_em);
    res.status(201).json(newEquipes_inspecao);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateEquipes_inspecao = async (req, res) => {
  try {
    const { inspecao_id, usuario_id, funcao, criado_em } = req.body;
    const updatedEquipes_inspecao = await equipes_inspecaoService.updateEquipes_inspecao(
      req.params.id,
      inspecao_id,
      usuario_id,
      funcao,
      criado_em
    );
    if (updatedEquipes_inspecao) {
      res.status(200).json(updatedEquipes_inspecao);
    } else {
      res.status(404).json({ error: 'Equipe não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteEquipes_inspecao = async (req, res) => {
  try {
    const deletedEquipes_inspecao = await equipes_inspecaoService.deleteEquipes_inspecao(req.params.id);
    if (deletedEquipes_inspecao) {
      res.status(200).json(deletedEquipes_inspecao);
    } else {
      res.status(404).json({ error: 'Equipe não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAllEquipes_inspecoes,
  getEquipes_inspecaoById,
  createEquipes_inspecao,
  updateEquipes_inspecao,
  deleteEquipes_inspecao
};

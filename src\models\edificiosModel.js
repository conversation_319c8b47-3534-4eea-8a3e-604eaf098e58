const db = require('../config/db');

class edificios {
  static async getAllEdificios() {
    const result = await db.query('SELECT * FROM edificios');
    return result.rows;
  }

  static async getEdificioById(id) {
    const result = await db.query('SELECT * FROM edificios WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createEdificios(data) {
    const result = await db.query(
      'INSERT INTO edificios (inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
      [data.inspecao_id, data.andares, data.bloco, data.tipo_edificio, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async updateEdificios(data) {
    const result = await db.query(
      'UPDATE edificios SET inspecao_id = $1, andares = $2, bloco =$3, tipo_edificio =$4, criado_por =$5, criado_em = $6, atualizado_em = $7 WHERE id = $8 RETURNING *',
      [data.inspecao_id, data.andares, data.bloco, data.tipo_edificio, data.criado_por, data.criado_em, data.atualizado_em, data.id]
    );
    return result.rows[0];
  }

  static async deleteEdificio(id) {
    const result = await db.query('DELETE FROM edificios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = edificios;

const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async () => {
    const sqlPath = path.join(__dirname, 'seed_inspecoes.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await db.query(`
      CREATE TABLE IF NOT EXISTS inspecoes (
        id SERIAL PRIMARY KEY,
        nome_edificio VARCHAR(200),
        endereco VARCHAR(300),
        tipo_edificio VARCHAR(50),
        torre_bloco VARCHAR(50),
        data_inicio DATE,
        data_fim DATE,
        status VARCHAR(50), -- nao_iniciado, em_andamento, concluido
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
    await db.query(sql);
    console.log('Dados inseridos em "inspeções" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS inspecoes CASCADE;');
  },
};

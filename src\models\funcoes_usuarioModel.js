const db = require('../config/db');

class funcoes_usuario {
  static async getAll() {
    const result = await db.query('SELECT * FROM funcoes_usuarios');
    return result.rows;
  }

  static async getById(id) {
    const result = await db.query('SELECT * FROM funcoes_usuarios WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(data) {
    const result = await db.query(
      'INSERT INTO funcoes_usuarios (usuario_id, funcao, criado_em) VALUES ($1, $2, $3) RETURNING *',
      [data.usuario_id, data.funcao, data.criado_em]
    );
    return result.rows[0];
  }

  static async update(id, data) {
    const result = await db.query(
      'UPDATE funcoes_usuarios SET usuario_id = $1, funcao = $2, criado_em = $3 WHERE id = $4 RETURNING *',
      [data.usuario_id, data.funcao, data.criado_em, id]
    );
    return result.rows[0];
  }

  static async delete(id) {
    const result = await db.query('DELETE FROM funcoes_usuarios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = funcoes_usuario;

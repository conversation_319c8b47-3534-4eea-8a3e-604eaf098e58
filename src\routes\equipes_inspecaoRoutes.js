const express = require('express');
const router = express.Router();
const equipes_inspecaoController = require('../controllers/equipes_inspecaoController');

router.get('/', equipes_inspecaoController.getAllEquipes_inspecoes);
router.get('/:id', equipes_inspecaoController.getEquipes_inspecaoById);
router.post('/', equipes_inspecaoController.createEquipes_inspecao);
router.put('/:id', equipes_inspecaoController.updateEquipes_inspecao);
router.delete('/:id', equipes_inspecaoController.deleteEquipes_inspecao);

module.exports = router;
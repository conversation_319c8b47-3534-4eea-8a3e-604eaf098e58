<%# Admin Dashboard Page - using main layout %>
<div class="gap-1 px-6 flex flex-1 justify-center py-5">
    <div class="layout-content-container flex flex-col w-full max-w-none flex-1">
        <%# Page header %>
        <div class="flex flex-wrap justify-between gap-3 p-4">
            <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">Painel</p>
                <p class="text-[#637688] text-sm font-normal leading-normal">
                    Visão geral de todas as atividades de inspeção
                </p>
            </div>
            <a href="/admin/nova-inspecao"
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#007bff] text-white text-sm font-medium leading-normal self-center">
                <span class="truncate">Nova Inspeção</span>
            </a>
        </div>

        <%# Statistics cards %>
        <div class="flex flex-wrap gap-4 p-4">
            <%
            const stats = [
                { label: 'Total de Inspeções', value: typeof dashboardData !== 'undefined' && dashboardData.totalInspecoes ? dashboardData.totalInspecoes : 3 },
                { label: 'Inspeções em Andamento', value: typeof dashboardData !== 'undefined' && dashboardData.inspecoesAndamento ? dashboardData.inspecoesAndamento : 1 },
                { label: 'Inspeções Concluídas', value: typeof dashboardData !== 'undefined' && dashboardData.inspecoesConcluidas ? dashboardData.inspecoesConcluidas : 1 },
                { label: 'Próximas Inspeções', value: typeof dashboardData !== 'undefined' && dashboardData.proximasInspecoes ? dashboardData.proximasInspecoes : 1 }
            ];
            %>
            <% stats.forEach(function(stat) { %>
                <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f0f2f4]">
                    <p class="text-[#111518] text-base font-medium leading-normal"><%= stat.label %></p>
                    <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight"><%= stat.value %></p>
                </div>
            <% }); %>
        </div>
        <%# Inspections overview section %>
        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
            Visão Geral das Inspeções
        </h2>

        <%# Search bar %>
        <div class="px-4 py-3">
            <label class="flex flex-col min-w-40 h-12 w-full">
                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                    <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                        data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                            fill="currentColor" viewBox="0 0 256 256">
                            <path
                                d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                            </path>
                        </svg>
                    </div>
                    <input placeholder="Buscar inspeções..." id="searchInput"
                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] h-full placeholder:text-[#637688] px-4 text-base font-normal leading-normal"
                        value="" />
                </div>
            </label>
        </div>
        <%# Filters %>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 py-3">
            <div>
                <label for="statusFilter" class="block text-sm font-medium text-gray-700">
                    Filtrar por Status
                </label>
                <select id="statusFilter" name="statusFilter"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                    <option value="">Todos</option>
                    <option value="Agendada">Agendada</option>
                    <option value="Em Andamento">Em Andamento</option>
                    <option value="Concluída">Concluída</option>
                </select>
            </div>
            <div>
                <label for="startDateFilter" class="block text-sm font-medium text-gray-700">
                    Data de Início
                </label>
                <input type="date" id="startDateFilter" name="startDateFilter"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
            </div>
            <div>
                <label for="endDateFilter" class="block text-sm font-medium text-gray-700">
                    Data de Término
                </label>
                <input type="date" id="endDateFilter" name="endDateFilter"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
            </div>
        </div>
        <%# Inspections table %>
        <div class="px-4 py-3 @container">
            <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                <table class="flex-1">
                    <thead>
                        <tr class="bg-white">
                            <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                ID da Inspeção
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] w-[250px] text-sm font-medium leading-normal">
                                Endereço
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                Status
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                Data de Início
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                Data de Término
                            </th>
                            <th class="px-4 py-3 text-center text-[#111518] w-[100px] text-sm font-medium leading-normal">
                                Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody id="inspectionsTableBody">
                        <%# Dynamic table rows - will be populated by JavaScript %>
                        <%# Default rows for demo purposes %>
                        <%
                        const defaultInspections = [
                            {
                                id: 'INSP-001',
                                address: 'Av. Paulista, 1500 - Bela Vista, São Paulo/SP',
                                status: 'Em Andamento',
                                startDate: '15/03/2024',
                                endDate: '30/03/2024'
                            },
                            {
                                id: 'INSP-002',
                                address: 'Rua Augusta, 2490 - Jardins, São Paulo/SP',
                                status: 'Agendada',
                                startDate: '25/03/2024',
                                endDate: '10/04/2024'
                            },
                            {
                                id: 'INSP-003',
                                address: 'Rua das Flores, 800 - Vila Madalena, São Paulo/SP',
                                status: 'Concluída',
                                startDate: '01/03/2024',
                                endDate: '15/03/2024'
                            }
                        ];

                        const inspections = typeof dashboardData !== 'undefined' && dashboardData.inspections ? dashboardData.inspections : defaultInspections;
                        %>

                        <% inspections.forEach(function(inspection) { %>
                            <tr class="border-t border-t-[#dce1e5]">
                                <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= inspection.id %>
                                </td>
                                <td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= inspection.address %>
                                </td>
                                <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                    <%
                                    let statusClass = '';
                                    switch(inspection.status) {
                                        case 'Em Andamento':
                                            statusClass = 'bg-[#fffbe6] text-[#faad14]';
                                            break;
                                        case 'Agendada':
                                            statusClass = 'bg-[#e6f7ff] text-[#007bff]';
                                            break;
                                        case 'Concluída':
                                            statusClass = 'bg-[#f6ffed] text-[#52c41a]';
                                            break;
                                        default:
                                            statusClass = 'bg-[#f5f5f5] text-[#8c8c8c]';
                                    }
                                    %>
                                    <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 <%= statusClass %> text-sm font-medium leading-normal w-full mx-auto">
                                        <span class="truncate"><%= inspection.status %></span>
                                    </button>
                                </td>
                                <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= inspection.startDate %>
                                </td>
                                <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                    <%= inspection.endDate %>
                                </td>
                                <td class="h-[72px] px-4 py-2 w-[100px] text-sm font-normal leading-normal text-center">
                                    <a href="/admin/detalhes-inspecao?id=<%= inspection.id %>" class="text-blue-600 hover:underline">
                                        Ver Detalhes
                                    </a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<%# Dashboard JavaScript %>
    <%# Dashboard filtering and interaction JavaScript %>
    document.addEventListener('DOMContentLoaded', function () {
        const statusFilter = document.getElementById('statusFilter');
        const startDateFilter = document.getElementById('startDateFilter');
        const endDateFilter = document.getElementById('endDateFilter');
        const searchInput = document.getElementById('searchInput');
        const tableRows = document.querySelectorAll('tbody tr');

        <%# Filter function %>
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            const startDateValue = startDateFilter.value;
            const endDateValue = endDateFilter.value;

            tableRows.forEach(row => {
                const idText = row.cells[0].textContent.toLowerCase();
                const addressText = row.cells[1].textContent.toLowerCase();
                const statusText = row.cells[2].textContent.trim();
                const dateText = row.cells[3].textContent.trim();

                <%# Parse date %>
                const [day, month, year] = dateText.split('/');
                const inspectionDate = new Date(`${year}-${month}-${day}`);

                <%# Check filters %>
                let matchesSearch = true;
                if (searchTerm) {
                    matchesSearch = idText.includes(searchTerm) || addressText.includes(searchTerm);
                }

                let matchesStatus = true;
                if (statusValue) {
                    matchesStatus = statusText === statusValue;
                }

                let matchesStartDate = true;
                if (startDateValue) {
                    const filterStartDate = new Date(startDateValue);
                    matchesStartDate = inspectionDate >= filterStartDate;
                }

                let matchesEndDate = true;
                if (endDateValue) {
                    const filterEndDate = new Date(endDateValue);
                    matchesEndDate = inspectionDate <= filterEndDate;
                }

                <%# Show/hide row based on filters %>
                if (matchesSearch && matchesStatus && matchesStartDate && matchesEndDate) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        <%# Attach event listeners %>
        statusFilter.addEventListener('change', filterTable);
        startDateFilter.addEventListener('input', filterTable);
        endDateFilter.addEventListener('input', filterTable);
        searchInput.addEventListener('input', filterTable);
    });
</script>
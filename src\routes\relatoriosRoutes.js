const express = require('express');
const router = express.Router();
const relatoriosController = require('../controllers/relatoriosController');

router.get('/', relatoriosController.getAllRelatorios);
router.get('/:id', relatoriosController.getRelatoriosById);
router.post('/', relatoriosController.createRelatorios);
router.put('/:id', relatoriosController.updateRelatorios);
router.delete('/:id', relatoriosController.deleteRelatorios);

module.exports = router;
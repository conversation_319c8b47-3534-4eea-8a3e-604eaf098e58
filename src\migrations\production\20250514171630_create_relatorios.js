const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS relatorios (
      id SERIAL PRIMARY KEY,
      inspecao_id INTEGER,
      titulo VARCHAR(100),
      status VARCHAR(100), -- ras<PERSON><PERSON>o, final
      gerado_por INTEGER,
      gerado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
      FOREIGN KEY (gerado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "relatorios" criada com sucesso.');
};
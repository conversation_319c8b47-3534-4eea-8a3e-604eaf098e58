# Inteli - Instituto de Tecnologia e Liderança 

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# InfraWatch

## Nexgrid

## :student: Integrantes: 
- <a href="https://www.linkedin.com/in/debora-<PERSON><PERSON>-nogue<PERSON>"><PERSON><PERSON><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/gabriel-mutter-de-souza-9a0131351/"><PERSON></a>
- <a href="https://www.linkedin.com/in/nicole-zanin-0a0361356/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/patrick-mura<PERSON><PERSON>-3572b3359/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/paulovictorbatista/"><PERSON></a>
- <a href="https://www.linkedin.com/in/rafael-nakahara-bb5100351/"><PERSON> Ryu Tati Nakahara</a> 

## :teacher: Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/profclaudioandre/">Claudio Fernando André</a>
### Instrutores
- <a href="https://www.linkedin.com/in/anacristinadossantos/">Ana Cristina dos Santos</a>
- <a href="https://www.linkedin.com/in/bruna-mayer/">Bruna Mayer Costa</a> 
- <a href="https://www.linkedin.com/in/diogo-martins-gonçalves-de-morais-96404732/">Diogo Martins Gonçalves de Morais</a> 
- <a href="https://www.linkedin.com/in/henrique-mohallem-paiva-6854b460/">Henrique Mohallem Paiva</a>
- <a href="https://www.linkedin.com/in/kizzyterra/">Kizzy Fernanda Terra Ferreira da Paz</a>

## 📝 Descrição

# 🏗️ Aplicação Web para Inspeção Predial

Esta é uma aplicação web desenvolvida para otimizar o processo de inspeção predial, permitindo o registro estruturado de informações e imagens durante as inspeções. A plataforma oferece uma experiência intuitiva e responsiva para smartphones, tablets e desktops, com geração automática de relatórios técnicos padronizados. Integrando modelos digitais das edificações e personalização de campos de inspeção, a solução visa agilizar, padronizar e garantir a qualidade da documentação predial, com foco em segurança e eficiência.

---

## ✅ Tecnologias Utilizadas

- **Node.js** – Ambiente de execução JavaScript no servidor.
- **Express** – Framework web leve e flexível para criação de APIs.
- **PostgreSQL** – Banco de dados relacional robusto e confiável.
- **Sequelize (opcional)** – ORM para abstração e manipulação do banco de dados.
- **Postman** – Ferramenta para testes de APIs RESTful.

---

## 🚀 Como Executar a Aplicação Localmente

### 1. Clonar o Repositório

```bash
git clone https://github.com/seu-usuario/seu-repositorio.git
cd seu-repositorio


## 📝 Link de demonstração

_Coloque aqui o link para seu projeto publicado e link para vídeo de demonstração_

## 📁 Estrutura de pastas

Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- <b>assets</b>: aqui estão os arquivos relacionados a elementos não-estruturados deste repositório, como imagens.

- <b>document</b>: aqui estão todos os documentos do projeto, como o Web Application  Document (WAD) bem como documentos complementares, na pasta "other".

- <b>src</b>: Todo o código fonte criado para o desenvolvimento do projeto de aplicação web.

- <b>README.md</b>: arquivo que serve como guia introdutório e explicação geral sobre o projeto e a aplicação (o mesmo arquivo que você está lendo agora).

## 💻 Configuração para desenvolvimento e execução do código

*Acrescentar as informações necessárias sobre pré-requisitos (IDEs, bibliotecas, serviços etc.) e instalação básica do projeto, descrevendo eventuais versões utilizadas. Colocar um passo a passo de como o leitor pode baixar o código e executar a aplicação a partir de sua máquina local.*

*exemplo de instruções*

Aqui encontram-se todas as instruções necessárias para a instalação de todos os programas, bibliotecas e ferramentas imprescindíveis para a configuração do ambiente de desenvolvimento.

1. Baixar e instalar o node.js: [https://nodejs.org/pt-br/](https://nodejs.org/pt-br/) (versão 16.15.1 LTS)
2. Clone o repositório em questão.
3. No modo administrador, abra o "prompt de comando" ou o "terminal" e, após, abra a pasta "src/backend" no diretório raiz do repositório clonado e digite o segundo comando:

```sh
npm install
```

Isso instalará todas as dependências definidas no arquivo <b>package.json</b> que são necessárias para rodar o projeto. Agora o projeto já está pronto para ser modificado. Caso ainda deseje iniciar a aplicação, digite o comando abaixo no terminal:

```sh
npm start
```
5. Agora você pode acessar a aplicação através do link http://localhost:1234/
6. O servidor está online.

## 🗃 Histórico de lançamentos

* 0.5.0 - XX/XX/2024
    * 
* 0.4.0 - XX/XX/2024
    * 
* 0.3.0 - XX/XX/2024
    * 
* 0.2.0 - XX/XX/2024
    * 
* 0.1.0 - XX/XX/2024
    *

## 📋 Licença/License
<a href="https://github.com/Inteli-College/2025-1B-T16-IN02-G01">InfraWatch</a> © 2025 by <a href="https://github.com/Inteli-College/2025-1B-T16-IN02-G01">Inteli, Débora Pereira Nogueira, Gabriel Mutter de Souza, Nicole Zanin Silva, Patrick Natan Murachovsky, Paulo Victor Batista de Souza, Rafael Ryu Tati Nakahara</a> is licensed under <a href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International</a><img src="https://mirrors.creativecommons.org/presskit/icons/cc.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;"><img src="https://mirrors.creativecommons.org/presskit/icons/by.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;">




# Inteli - Instituto de Tecnologia e Liderança

<p align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança">
  </a>
</p>

# 🏗️ InfraWatch

## 👥 Nexgrid

### :student: Integrantes
- [Débora Pereira Nogueira](https://www.linkedin.com/in/debora-pereira-nogueira)
- [Gabriel Mutter de Souza](https://www.linkedin.com/in/gabriel-mutter-de-souza-9a0131351/)
- [Nicole Zanin Silva](https://www.linkedin.com/in/nicole-zanin-0a0361356/)
- [Patrick Natan Murachovsky](https://www.linkedin.com/in/patrick-murachovsky-3572b3359/)
- [Paulo Victor Batista de Souza](https://www.linkedin.com/in/paulovictorbatista/)
- [Rafael Ryu Tati Nakahara](https://www.linkedin.com/in/rafael-nakahara-bb5100351/)

### :teacher: Professores

**Orientador(a):**
- [Claudio Fernando André](https://www.linkedin.com/in/profclaudioandre/)

**Instrutores:**
- [Ana Cristina dos Santos](https://www.linkedin.com/in/anacristinadossantos/)
- [Bruna Mayer Costa](https://www.linkedin.com/in/bruna-mayer/)
- [Diogo Martins Gonçalves de Morais](https://www.linkedin.com/in/diogo-martins-gonçalves-de-morais-96404732/)
- [Henrique Mohallem Paiva](https://www.linkedin.com/in/henrique-mohallem-paiva-6854b460/)
- [Kizzy Fernanda Terra Ferreira da Paz](https://www.linkedin.com/in/kizzyterra/)

---

## 📝 Descrição do Projeto

O **InfraWatch** é uma aplicação web desenvolvida para otimizar o processo de inspeção predial, permitindo o registro estruturado de informações e imagens durante as vistorias. A plataforma oferece uma experiência intuitiva e responsiva em smartphones, tablets e desktops, com geração automática de relatórios técnicos padronizados. Integrando modelos digitais das edificações e personalização de campos de inspeção, a solução visa agilizar, padronizar e garantir a qualidade da documentação predial, com foco em segurança e eficiência.

---

## 📁 Estrutura do Repositório

- `assets/` – Imagens e elementos visuais do projeto.
- `document/` – Documentos técnicos e administrativos.
- `src/backend/` – Código-fonte do back-end.
- `README.md` – Documento atual com instruções e informações do projeto.

---

## 🧰 Tecnologias Utilizadas

- **Node.js** – Ambiente de execução JavaScript no back-end.
- **Express.js** – Framework para APIs RESTful.
- **PostgreSQL** – Banco de dados relacional.
- **dotenv** – Variáveis de ambiente.
- **Nodemon** – Ferramenta para facilitar o desenvolvimento com reinicialização automática do servidor.

---

## ⚙️ Como executar a aplicação em ambiente local

### 📦 Instalação e dependências

1. Clone o repositório:

```bash
git clone https://github.com/Inteli-College/2025-1B-T16-IN02-G01.git
cd 2025-1B-T16-IN02-G01/src/backend


2. Instale as dependências:

**npm install**


3. Configuração do banco de dados PostgreSQL
Crie o banco de dados no PostgreSQL:

CREATE DATABASE infrawatch_db;


4. Configure as variáveis de ambiente criando um arquivo .env dentro de src/backend com o conteúdo:

DB_HOST=localhost
DB_PORT=5432
DB_USER=seu_usuario
DB_PASSWORD=sua_senha
DB_NAME=infrawatch_db
(Substitua seu_usuario e sua_senha pelas credenciais do seu banco.)


5. (Opcional) Execute o script SQL para criar as tabelas e dados iniciais (caso exista):

psql -U seu_usuario -d infrawatch_db -f scripts/init.sql



6. ▶️ Executar o servidor
Inicie o servidor com:

npm start
A aplicação estará disponível em:

http://localhost:1234/
Para desenvolvimento com reinicialização automática, use:

npx nodemon server.js

🧪 Teste dos Endpoints
Utilize o Postman, Insomnia ou outro cliente HTTP para testar os endpoints da API.

Exemplos de rotas:

Método	Endpoint	Função
GET	/api/vistorias	Lista todas as vistorias
POST	/api/vistorias	Cria uma nova vistoria
GET	/api/vistorias/:id	Detalha uma vistoria específica
PUT	/api/vistorias/:id	Atualiza uma vistoria existente
DELETE	/api/vistorias/:id	Remove uma vistoria

Exemplo de corpo (POST):

{
  "titulo": "Inspeção elétrica",
  "data": "2025-05-28",
  "responsavel": "Eng. Carla Ramos",
  "observacoes": "Sistema elétrico dentro das normas."
}

📋 Histórico de Versões
0.5.0 – [Data de lançamento da versão final]

0.4.0 – [Data da versão beta]

0.3.0 – Melhorias no CRUD e endpoints REST

0.2.0 – Criação da estrutura back-end com Express e banco de dados

0.1.0 – Inicialização do projeto
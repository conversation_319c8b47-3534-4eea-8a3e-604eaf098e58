const db = require('../config/db');

class ambientes {
  static async getAllAmbiente() {
    const result = await db.query('SELECT * FROM ambientes');
    return result.rows;
  }

  static async getAmbienteById(id) {
    const result = await db.query('SELECT * FROM ambientes WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createAmbiente(data) {
    const result = await db.query(
      'INSERT INTO ambientes (pavimento_id, nome, criado_por, criado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [data.pavimento_id, data.nome, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async updateAmbiente(data) {
    const result = await db.query(
      'UPDATE ambientes SET pavimento_id =$1, nome =$2, criado_por =$3, criado_em =$4, atualizado_em =$5 WHERE id = $6 RETURNING *',
      [data.pavimento_id, data.nome, data.criado_por, data.criado_em, data.atualizado_em, data.id]
    );
    return result.rows[0];
  }

  static async deleteAmbiente(id) {
    const result = await db.query('DELETE FROM ambientes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = ambientes;

<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Inspeção - Inspetor</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/listaInspecoes">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/listaInspecoes">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/relatorios">Relatórios</a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>                    <a href="/perfil" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('/images/perfil.png');">
                    </a>
                </div>
            </header>

            <main class="flex-1" x-data="inspectionDetails()">
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                        <!-- Cabeçalho da Inspeção -->
                        <div class="flex flex-wrap justify-between gap-3 p-4">
                            <div class="flex min-w-72 flex-col gap-3">
                                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">
                                    <span x-text="inspection.name"></span>
                                </p>
                                <p class="text-[#637588] text-sm font-normal leading-normal">
                                    ID: <span x-text="inspection.id"></span> | Status: 
                                    <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                          :class="getStatusColor(inspection.status)" 
                                          x-text="inspection.status"></span>
                                </p>
                            </div>
                            <div class="flex gap-3">
                                <button @click="toggleEditMode()" 
                                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                    <span x-text="editMode ? 'Salvar' : 'Editar'"></span>
                                </button>
                                <button @click="generateReport()" 
                                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-bold leading-normal tracking-[0.015em]">
                                    <span>Gerar Relatório</span>
                                </button>
                            </div>
                        </div>

                        <!-- Informações Básicas -->
                        <div class="grid grid-cols-2 gap-4 px-4 py-4">
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Cliente</p>
                                <input x-model="inspection.client" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                       type="text">
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Endereço</p>
                                <input x-model="inspection.address" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                       type="text">
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Tipo de Inspeção</p>
                                <select x-model="inspection.type" 
                                        :disabled="!editMode"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed">
                                    <option value="Estrutural">Estrutural</option>
                                    <option value="Elétrica">Elétrica</option>
                                    <option value="Hidráulica">Hidráulica</option>
                                    <option value="Completa">Completa</option>
                                </select>
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Data de Início</p>
                                <input x-model="inspection.startDate" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                       type="date">
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Data de Fim</p>
                                <input x-model="inspection.endDate" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                       type="date">
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Progresso</p>
                                <div class="flex items-center gap-2">
                                    <input x-model="inspection.progress" 
                                           :disabled="!editMode"
                                           class="w-20 px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                           type="number" min="0" max="100">
                                    <span>%</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${inspection.progress}%`"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Descrição -->
                        <div class="px-4 py-4">
                            <p class="text-[#111518] text-base font-medium leading-normal mb-2">Descrição</p>
                            <textarea x-model="inspection.description" 
                                      :disabled="!editMode"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                      rows="4"
                                      placeholder="Descrição detalhada da inspeção..."></textarea>
                        </div>

                        <!-- Fotos da Inspeção -->
                        <div class="px-4 py-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-[#111518] text-lg font-bold leading-tight">Fotos da Inspeção</h3>
                                <button x-show="editMode" @click="addPhoto('inspection')" 
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    Adicionar Foto
                                </button>
                            </div>
                            <div class="grid grid-cols-3 gap-4" x-show="inspection.photos && inspection.photos.length > 0">
                                <template x-for="(photo, index) in inspection.photos" :key="index">
                                    <div class="relative group">
                                        <img :src="photo.url" :alt="photo.description" 
                                             class="w-full h-32 object-cover rounded-lg cursor-pointer"
                                             @click="viewPhoto(photo)">
                                        <div x-show="editMode" class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                            <button @click="removePhoto('inspection', index)" 
                                                    class="bg-red-500 text-white rounded-full p-1 hover:bg-red-600">
                                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <p class="mt-2 text-sm text-gray-600" x-text="photo.description"></p>
                                    </div>
                                </template>
                            </div>
                            <div x-show="!inspection.photos || inspection.photos.length === 0" 
                                 class="text-center py-8 text-gray-500">
                                Nenhuma foto adicionada ainda
                            </div>
                        </div>

                        <!-- Estrutura Hierárquica -->
                        <div class="px-4 py-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight mb-4">Estrutura da Inspeção</h3>
                            <div class="space-y-2">
                                <template x-for="building in inspection.buildings" :key="building.id">
                                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                                        <div class="p-4 bg-gray-50 cursor-pointer flex justify-between items-center"
                                             @click="building.expanded = !building.expanded">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-5 h-5 transition-transform" 
                                                     :class="building.expanded ? 'rotate-90' : ''" 
                                                     fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                                                </svg>
                                                <h4 class="font-medium" x-text="building.name"></h4>
                                            </div>
                                            <button @click.stop="viewBuildingDetails(building.id)" 
                                                    class="text-blue-600 hover:text-blue-800">
                                                Ver Detalhes
                                            </button>
                                        </div>
                                        <div x-show="building.expanded" class="border-t">
                                            <template x-for="floor in building.floors" :key="floor.id">
                                                <div class="ml-4 border-l-2 border-gray-200">
                                                    <div class="p-3 bg-gray-25 cursor-pointer flex justify-between items-center"
                                                         @click="floor.expanded = !floor.expanded">
                                                        <div class="flex items-center gap-3">
                                                            <svg class="w-4 h-4 transition-transform" 
                                                                 :class="floor.expanded ? 'rotate-90' : ''" 
                                                                 fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                                                            </svg>
                                                            <span class="text-sm" x-text="floor.name"></span>
                                                        </div>
                                                        <button @click.stop="viewFloorDetails(floor.id, building.id)" 
                                                                class="text-blue-600 hover:text-blue-800 text-sm">
                                                            Ver Detalhes
                                                        </button>
                                                    </div>
                                                    <div x-show="floor.expanded" class="ml-4">
                                                        <template x-for="environment in floor.environments" :key="environment.id">
                                                            <div class="p-2 border-b border-gray-100 flex justify-between items-center">
                                                                <span class="text-sm" x-text="environment.name"></span>
                                                                <button @click="viewEnvironmentDetails(environment.id, floor.id, building.id)" 
                                                                        class="text-blue-600 hover:text-blue-800 text-xs">
                                                                    Ver Detalhes
                                                                </button>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal para visualizar/editar fotos -->
    <div x-show="showPhotoModal" @click.away="closePhotoModal()" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">Foto</h3>
                <button @click="closePhotoModal()" class="text-gray-500 hover:text-gray-700">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div x-show="currentPhoto">
                <img :src="currentPhoto?.url" :alt="currentPhoto?.description" 
                     class="w-full max-h-96 object-contain mb-4">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium mb-1">Descrição</label>
                        <textarea x-model="currentPhoto.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                                  rows="3"></textarea>
                    </div>
                    <div x-show="editMode" class="flex gap-3">
                        <button @click="savePhotoDescription()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Salvar
                        </button>
                        <button @click="closePhotoModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function inspectionDetails() {
            return {
                editMode: false,
                showPhotoModal: false,
                currentPhoto: null,
                inspection: {
                    id: '',
                    name: '',
                    client: '',
                    address: '',
                    type: '',
                    status: '',
                    startDate: '',
                    endDate: '',
                    progress: 0,
                    description: '',
                    photos: [],
                    buildings: []
                },

                init() {
                    this.loadInspection();
                },

                loadInspection() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const inspectionId = urlParams.get('id');
                      // Dados simulados
                    const inspectionsData = {
                        'INSP-001': {
                            id: 'INSP-001',
                            name: 'Inspeção Edifício Central',
                            client: 'Edifício Central',
                            address: 'Rua das Palmeiras, 123, Cidade Exemplo',
                            type: 'Estrutural',
                            status: 'Em Andamento',
                            startDate: '2024-03-15',
                            endDate: '2024-03-30',
                            progress: 45,
                            description: 'Inspeção estrutural completa do edifício incluindo fundações, estrutura de concreto e instalações.',
                            contactName: 'Ana Paula',
                            contactEmail: '<EMAIL>',
                            contactPhone: '(11) 98765-4321',
                            coordinator: 'Carlos Silva',
                            engineer: 'Carlos Silva',
                            creationDate: '01/03/2024',
                            inspectors: ['João Kleber', 'Maria Fernanda'],
                            photos: [
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Fachada principal do edifício'
                                },
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Entrada principal'
                                }
                            ],
                            buildings: [
                                {
                                    id: 'BUILD-001',
                                    name: 'Edifício Principal',
                                    expanded: false,
                                    floors: [
                                        {
                                            id: 'FLOOR-001',
                                            name: 'Térreo',
                                            expanded: false,
                                            environments: [
                                                { id: 'ENV-001', name: 'Hall de Entrada' },
                                                { id: 'ENV-002', name: 'Recepção' }
                                            ]
                                        },
                                        {
                                            id: 'FLOOR-002',
                                            name: '1º Andar',
                                            expanded: false,                                            environments: [
                                                { id: 'ENV-003', name: 'Sala 101' },
                                                { id: 'ENV-004', name: 'Sala 102' }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        'INSP-002': {
                            id: 'INSP-002',
                            name: 'Inspeção Shopping Plaza',
                            client: 'Shopping Plaza',
                            address: 'Avenida Central, 456, Vila Modelo',
                            type: 'Elétrica',
                            status: 'Agendada',
                            startDate: '2025-06-10',
                            endDate: '2025-06-12',
                            progress: 0,
                            description: 'Inspeção das instalações elétricas do shopping.',
                            contactName: 'Pedro Martins',
                            contactEmail: '<EMAIL>',
                            contactPhone: '(21) 91234-5678',
                            coordinator: 'Maria Santos',
                            engineer: 'Fernanda Costa',
                            creationDate: '05/03/2024',
                            inspectors: ['Carlos Mendes', 'Ana Lima'],
                            photos: [],
                            buildings: []
                        },
                        'INSP-003': {
                            id: 'INSP-003',
                            name: 'Inspeção Residencial Aurora',
                            client: 'Governo Municipal',
                            address: 'Praça da Matriz, 789, Centro',
                            type: 'Viaduto',
                            status: 'Concluída',
                            startDate: '2025-05-15',
                            endDate: '2025-05-17',
                            progress: 100,
                            description: 'Inspeção completa do viaduto municipal incluindo estrutura e fundações.',
                            contactName: 'Sofia Pereira',
                            contactEmail: '<EMAIL>',
                            contactPhone: '(31) 99999-8888',
                            coordinator: 'Ana Lima',
                            engineer: 'Roberto Almeida',
                            creationDate: '20/02/2024',
                            inspectors: ['Fernanda Rocha', 'Paulo Santos'],
                            photos: [],
                            buildings: []
                        }
                    };

                    if (inspectionId && inspectionsData[inspectionId]) {
                        this.inspection = { ...inspectionsData[inspectionId] };
                    }
                },

                toggleEditMode() {
                    if (this.editMode) {
                        this.saveInspection();
                    }
                    this.editMode = !this.editMode;
                },

                saveInspection() {
                    // Aqui seria feita a requisição para salvar no backend
                    console.log('Salvando inspeção:', this.inspection);
                    alert('Inspeção salva com sucesso!');
                },

                getStatusColor(status) {
                    const colors = {
                        'Agendada': 'bg-yellow-100 text-yellow-800',
                        'Em Andamento': 'bg-blue-100 text-blue-800',
                        'Concluída': 'bg-green-100 text-green-800',
                        'Cancelada': 'bg-red-100 text-red-800'
                    };
                    return colors[status] || 'bg-gray-100 text-gray-800';
                },

                addPhoto(type) {
                    // Simular upload de foto
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            const url = URL.createObjectURL(file);
                            this.inspection.photos.push({
                                url: url,
                                description: `Nova foto - ${new Date().toLocaleString()}`
                            });
                        }
                    };
                    input.click();
                },

                removePhoto(type, index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.inspection.photos.splice(index, 1);
                    }
                },

                viewPhoto(photo) {
                    this.currentPhoto = { ...photo };
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.currentPhoto = null;
                },

                savePhotoDescription() {
                    // Encontrar e atualizar a foto na lista
                    const index = this.inspection.photos.findIndex(p => p.url === this.currentPhoto.url);
                    if (index !== -1) {
                        this.inspection.photos[index].description = this.currentPhoto.description;
                    }
                    this.closePhotoModal();
                },

                generateReport() {
                    window.location.href = `relatorios.html?inspection=${this.inspection.id}`;
                },

                viewBuildingDetails(buildingId) {
                    window.location.href = `detalhesEdificio.html?id=${buildingId}&inspection=${this.inspection.id}`;
                },

                viewFloorDetails(floorId, buildingId) {
                    window.location.href = `detalhesPavimento.html?id=${floorId}&building=${buildingId}&inspection=${this.inspection.id}`;
                },

                viewEnvironmentDetails(environmentId, floorId, buildingId) {
                    window.location.href = `detalhesAmbiente.html?id=${environmentId}&floor=${floorId}&building=${buildingId}&inspection=${this.inspection.id}`;
                }
            }
        }

        function goBack() {
            window.location.href = '/listaInspecoes';
        }
    </script>
</body>

</html>
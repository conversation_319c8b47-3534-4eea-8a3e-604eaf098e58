# Teste da rota GET /users
GET http://localhost:3000/users
Accept: application/json

###

# Teste da rota GET /users/:id
GET http://localhost:3000/users/39602f41-b43e-4fa8-b7f7-34c7a516740b
Accept: application/json

###

# Teste da rota POST /users
POST http://localhost:3000/users
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

###

# Teste da rota PUT /users/:id
PUT http://localhost:3000/users/39602f41-b43e-4fa8-b7f7-34c7a516740b
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

###

# Teste da rota DELETE /users/:id
DELETE http://localhost:3000/users/39602f41-b43e-4fa8-b7f7-34c7a516740b
Accept: application/json

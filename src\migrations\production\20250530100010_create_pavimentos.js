const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS pavimentos(
      id SERIAL PRIMARY KEY,
      edificio_id INTEGER,
      andar VARCHAR(50), --<PERSON><PERSON><PERSON><PERSON>, 1ºandar, 2ºandar, etc
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (edificio_id) REFERENCES edificios(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "pavimentos" criada com sucesso.');
};

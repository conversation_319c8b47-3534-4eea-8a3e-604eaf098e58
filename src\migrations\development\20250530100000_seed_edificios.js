const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async () => {
    const sqlPath = path.join(__dirname, 'seed_edificios.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await db.query(`
      CREATE TABLE IF NOT EXISTS edificios(
        id SERIAL PRIMARY KEY,
        inspecao_id INTEGER,
        numero VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
        Andares INTEGER,
        Bloco VARCHAR(50), -- Bloco A, Bloco B, etc
        tipo_edificio VARCHAR(50), -- residencial, comercial, misto
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
    console.log('Dados inseridos em "edificios" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS edificios CASCADE;');
  },
};

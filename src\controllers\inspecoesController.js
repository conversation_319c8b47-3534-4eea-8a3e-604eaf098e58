// controllers/inspecoesController.js

const inspecoesModel = require('../models/inspecoesModel');

const getAllInspecoes = async (req, res) => {
  try {
    const inspecoes = await inspecoesService.getAllInspecoess();
    res.status(200).json(inspecoes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getInspecoesById = async (req, res) => {
  try {
    const inspecoes = await inspecoesService.getInspecoesById(req.params.id);
    if (inspecoes) {
      res.status(200).json(inspecoes);
    } else {
      res.status(404).json({ error: 'Inspeção não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createInspecoes = async (req, res) => {
  try {
    const { name, email } = req.body;
    const newInspecoes = await inspecoesrService.createInspecoes(name, email, senha, criado_em );
    res.status(201).json(newInspecoes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateInspecoes = async (req, res) => {
  try {
    const { name, email } = req.body;
    const updatedInspecoes = await inspecoesService.updateInspecoes(req.params.id, name, email, senha, criado_em);
    if (updatedInspecoes) {
      res.status(200).json(updatedInspecoes);
    } else {
      res.status(404).json({ error: 'Inspeção não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteInspecoes = async (req, res) => {
  try {
    const deletedInspecoes = await inspecoesService.deleteInspecoes(req.params.id);
    if (deletedInspecoes) {
      res.status(200).json(deletedInspecoes);
    } else {
      res.status(404).json({ error: 'Inspeção não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAllInspecoes,
  getInspecoesById,
  createInspecoes,
  updateInspecoes,
  deleteInspecoes
};
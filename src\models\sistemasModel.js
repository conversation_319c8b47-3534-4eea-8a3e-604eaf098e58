const db = require('../config/db');

class sistemas
 {
  static async getAllSistema() {
    const result = await db.query('SELECT * FROM sistemas');
    return result.rows;
  }

  static async getSistemaById(id) {
    const result = await db.query('SELECT * FROM sistemas WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async createSistema(data) {
    const result = await db.query(
      'INSERT INTO sistemas (ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [data.ambiente_id, data.tipo, data.descricao, data.criado_por, data.criado_em, data.atualizado_em]
    );
    return result.rows[0];
  }

  static async updateSistema(data) {
    const result = await db.query(
      'UPDATE sistemas SET ambiente_id =$1, tipo =$2, descricao =$3, criado_por =$4, criado_em =$5, atualizado_em =$6 WHERE id = $7 RETURNING *',
      [data.ambiente_id, data.tipo, data.descricao, data.criado_por, data.criado_em, data.atualizado_em, data.id]
    );
    return result.rows[0];
  }

  static async deleteSistema(id) {
    const result = await db.query('DELETE FROM sistemas WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = sistemas;

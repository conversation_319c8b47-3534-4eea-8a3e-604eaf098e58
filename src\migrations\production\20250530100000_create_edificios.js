const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS edificios(
      id SERIAL PRIMARY KEY,
      inspecao_id INTEGER,
      numero VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
      Andares INTEGER,
      Bloco VARCHAR(50), -- Bloco A, Bloco B, etc
      tipo_edificio VARCHAR(50), -- residencial, comercial, misto
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "edificios" criada com sucesso.');
};

const express = require('express');
const router = express.Router();
const fotosControllers = require('../controllers/fotosControllers');

router.get('/', fotosControllers.getAllFoto);
router.get('/:id', fotosControllers.getFotoById);
router.post('/', fotosControllers.createFoto);
router.put('/:id', fotosControllers.updateFoto);
router.delete('/:id', fotosControllers.deleteFoto);

module.exports = router;


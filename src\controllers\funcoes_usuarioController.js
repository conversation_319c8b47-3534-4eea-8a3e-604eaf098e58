const funcoes_usuarioModel = require('../models/funcoes_usuarioModel');

const getAllFuncoes_usuarios = async (req, res) => {
  try {
    const funcoes_usuarios = await funcoes_usuarioService.getAllFuncoes_usuarios();
    res.status(200).json(funcoes_usuarios);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getFuncoes_usuarioById = async (req, res) => {
  try {
    const funcoes_usuario = await funcoes_usuarioService.getFuncoes_usuarioById(req.params.id);
    if (funcoes_usuario) {
      res.status(200).json(funcoes_usuario);
    } else {
      res.status(404).json({ error: 'Função não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createFuncoes_usuario = async (req, res) => {
  try {
    const { usuario_id, funcao, criado_em } = req.body;
    const newFuncoes_usuario = await funcoes_usuarioService.createFuncoes_usuario(usuario_id, funcao, criado_em);
    res.status(201).json(newFuncoes_usuario);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateFuncoes_usuario = async (req, res) => {
  try {
    const { usuario_id, funcao, criado_em } = req.body;
    const updatedFuncoes_usuario = await funcoes_usuarioService.updateFuncoes_usuario(
      req.params.id,
      usuario_id,
      funcao,
      criado_em
    );
    if (updatedFuncoes_usuario) {
      res.status(200).json(updatedFuncoes_usuario);
    } else {
      res.status(404).json({ error: 'Função não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteFuncoes_usuario = async (req, res) => {
  try {
    const deletedFuncoes_usuario = await funcoes_usuarioService.deleteFuncoes_usuario(req.params.id);
    if (deletedFuncoes_usuario) {
      res.status(200).json(deletedFuncoes_usuario);
    } else {
      res.status(404).json({ error: 'Função não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAllFuncoes_usuarios,
  getFuncoes_usuarioById,
  createFuncoes_usuario,
  updateFuncoes_usuario,
  deleteFuncoes_usuario
};

const express = require('express');
const router = express.Router();
const funcoes_usuarioController = require('../controllers/funcoes_usuarioController');

router.get('/', funcoes_usuarioController.getAllFuncoes_usuarios);
router.get('/:id', funcoes_usuarioController.getFuncoes_usuarioById);
router.post('/', funcoes_usuarioController.createFuncoes_usuario);
router.put('/:id', funcoes_usuarioController.updateFuncoes_usuario);
router.delete('/:id', funcoes_usuarioController.deleteFuncoes_usuario);

module.exports = router;
// controllers/edificioControllers.js

const pavimentosModel = require('../models/pavimentosModel');

const getAllPavimento = async (req, res) => {
  try {
    const pavimento = await pavimentosModel.getAllPavimento();
    res.status(200).json(pavimento);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getPavimentoById = async (req, res) => {
  try {
    const pavimento = await pavimentosModel.getPavimentoById(req.params.id);
    if (pavimento) {
      res.status(200).json(pavimento);
    } else {
      res.status(404).json({ error: 'Pavimento não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createPavimento = async (req, res) => {
  try {
    const {edificio_id, andar, criado_por, criado_em, atualizado_em} = req.body;
    const newPavimento = await pavimentosModel.createPavimento( edificio_id, andar, criado_por, criado_em, atualizado_em);
    res.status(201).json(newPavimento);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updatePavimento = async (req, res) => {
  try {
    const { edificio_id, andar, criado_por, criado_em, atualizado_em} = req.body;
    const updatedPavimento = await pavimentosModel.updatePavimento(req.params.id,  edificio_id, andar, criado_por, criado_em, atualizado_em);
    if (updatedPavimento) {
      res.status(200).json(updatedPavimento);
    } else {
      res.status(404).json({ error: 'Pavimento não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deletePavimento = async (req, res) => {
  try {
    const deletedPavimento = await pavimentosModel.deletePavimento(req.params.id);
    if (deletedPavimento) {
      res.status(200).json(deletedPavimento);
    } else {
      res.status(404).json({ error: 'Pavimento não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllPavimento,
getPavimentoById,
createPavimento,
updatePavimento,
deletePavimento

};

const db = require('../../config/db');

module.exports = {
  up: async () => {
    await db.query(`
      CREATE TABLE IF NOT EXISTS equipes_inspecao (
        id SERIAL PRIMARY KEY,
        inspecao_id INTEGER,
        usuario_id INTEGER,
        funcao VARCHAR(50), -- inspetor, coordenador, administrador
        criado_em TIMESTAMP,
        FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
      );
    `);
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS equipes_inspecao CASCADE;');
  },
};

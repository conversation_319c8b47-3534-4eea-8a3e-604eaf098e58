<img src="../assets/logointeli.png">


# WAD - Web Application Document - Módulo 2 - Inteli

**_Os trechos em itálico servem apenas como guia para o preenchimento da seção. Por esse motivo, não devem fazer parte da documentação final_**

## Nexgrid

#### Nomes dos integrantes do grupo

- <a href="https://www.linkedin.com/in/debora-pereira-nogueira"><PERSON><PERSON><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/gabriel-mutter-de-souza-9a0131351/"><PERSON></a>
- <a href="https://www.linkedin.com/in/nicole-zanin-0a0361356/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/patrick-mura<PERSON><PERSON>-3572b3359/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/paulovictorbatista/"><PERSON></a>
- <a href="https://www.linkedin.com/in/rafael-nakahara-bb5100351/">Rafael Ryu Tati Nakahara</a> 

## Sumário

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](#c8)

[Anexos](#c9)

<br>


# <a name="c1"></a>1. Introdução (sprints 1 a 5)

O Instituto de Pesquisas Tecnológicas (IPT) identificou uma grande problemática: a complexidade e a diversidade das necessidades de cada edifício. Cada um apresenta características únicas, como idade, materiais e sistemas instalados, o que dificulta a padronização da coleta e organização dos textos e fotos, e a geração ágil dos relatórios técnicos. Além disso, a ausência de diretrizes uniformes e a necessidade de proteger dados sensíveis vêm impulsionando o uso da tecnologia nessa área.

Para atender a essa demanda, foi desenvolvida uma aplicação web que padroniza o fluxo de inspeção predial, desde o cadastro de ambientes (por exemplo: pisos, paredes, fachadas) até o registro organizado dos problemas e ocorrências com textos e fotos vinculadas.

A crescente exigência de órgãos reguladores por comprovação documental torna urgente a modernização dos métodos de inspeção, atualmente ainda amplamente baseados em processos manuais e planilhas desconexas. Sem uma rastreabilidade adequada, decisões de manutenção ficam sujeitas a atrasos e podem gerar custos elevados decorrentes da má organização dos dados. Ao estruturar as informações coletadas (texto e imagem) em um único lugar, o projeto amplia a capacidade da análise temporal e comparativa, viabilizando estudos de tendência de degradação.

# <a name="c2"></a>2. Visão Geral da Aplicação Web (sprint 1)

## 2.1. Escopo do Projeto (sprints 1 e 4)

### 2.1.1. Modelo de 5 Forças de Porter (sprint 1)

As 5 Forças de Porter[₁](#ref-5forcas) são uma ferramenta de análise estratégica criada por Michael Porter que permite entender o nível de competitividade de um setor. Ao analisar essas forças, é possível identificar os fatores externos que impactam diretamente a capacidade de uma organização se manter competitiva e sustentável no mercado.

A análise das 5 Forças de Porter é importante para o IPT porque oferece uma visão estratégica do ambiente em que a instituição atua[₂](#ref-iptQuemSomos), permitindo identificar riscos e pressões competitivas. Mesmo sendo um órgão público, o IPT precisa manter sua relevância frente a universidades, empresas e startups. Essa análise ajuda a direcionar investimentos, melhorar a eficiência, fortalecer o posicionamento institucional e planejar o futuro de forma mais alinhada às demandas do mercado e da sociedade.

Ademais, para o trabalho desenvolvido em parceria com o IPT, essa análise é ainda mais relevante, pois oferece uma compreensão mais profunda do contexto em que a instituição está inserida. Isso permite que as decisões, propostas e soluções estejam alinhadas com os desafios reais enfrentados pelo instituto, aumentando as chances de gerar valor estratégico para a parceria.

Baseado nisso, é válido por exemplo, a análise da rivalidade entre concorrentes, que é moderada, pois embora existam outras instituições como INT, Embrapii e pesquisas internas de universidades, o IPT se destaca pela credibilidade, com mais de 100 anos de atuação e infraestrutura de ponta, o que o torna referência em projetos técnicos complexos, especialmente no setor público. Portanto, existem outras análises de mercado relevantes, de acordo com Porter:

<div align = 'center'>
<img src = '../assets/5forcas.png'>
</div>

### 2.1.2. Análise SWOT da Instituição Parceira (sprint 1)

A análise SWOT é uma ferramenta que ajuda a entender melhor um negócio ou projeto através de quatro aspectos essenciais:

Strengths (Forças): Quais são os diferenciais e pontos mais fortes do négocio ou processo.
Weaknesses (Fraquezas): O que pode ser melhorado e onde estão os desafios internos.
Opportunities (Oportunidades): Quais fatores externos podem ajudar a crescer.
Threats (Ameaças): O que pode atrapalhar o sucesso da operação e quais os riscos do mercado.

<div align = 'center'>
<img src = '../assets/swot.png'>
</div>

### 2.1.3. Solução (sprints 1 a 5)

**1. Problema a ser resolvido**
A Inspeção predial enfrenta vários problemas como diversidade entre prédios, falta de padronização, incompatibilidade entre sistemas, riscos à segurança dos dados, excesso de informações e pouca colaboração entre áreas. Essas dificuldades surgem porque cada prédio funciona de forma única, não há modelo unificado, e o desenvolvimento exige integração entre especialistas da construção, gestão e tecnologia.

**2. Dados disponíveis (mencionar fonte e conteúdo; se não houver, indicar “não se aplica”)**
Serão usados dados reais de inspeções feitas pelo IPT, como textos e imagens de problemas em prédios. Esses dados não identificam construções reais e servem apenas como apoio ao desenvolvimento do app. O uso é restrito ao projeto, sendo proibido compartilhar externamente, garantindo segurança e foco exclusivo na criação da aplicação.

**3. Solução proposta**
A proposta é criar uma aplicação Web acessível por celular, tablet e PC, que padronize e otimize inspeções prediais. Com interface intuitiva, banco de dados estruturado, relatórios automáticos, personalização por tipo de prédio e segurança dos dados, a solução busca ser eficiente, confiável e adaptável, atendendo técnicos, gestores e profissionais da construção.

**Pensando no Futuro: Integração, Crescimento e Cuidados com a Aplicação**

Para que a aplicação desenvolvida seja bem aproveitada pelo IPT e possa evoluir, alguns aspectos sobre sua integração com os sistemas existentes, capacidade de crescimento e manutenção futura são importantes:

*   **Conversando com os Sistemas Atuais do IPT (Compatibilidade):**
    A presente aplicação, desenvolvida com tecnologias como Node.js, foi projetada com uma estrutura de dados organizada, o que é um ponto fundamental para facilitar a integração. Embora as tecnologias específicas possam diferir, a organização lógica dos dados busca ser compatível com as necessidades de intercâmbio de informações.
    Para que a aplicação converse bem com o ecossistema do IPT, a principal forma de integração seria através de APIs (pontes de comunicação entre sistemas) ou pela exportação/importação de dados em formatos comuns. A estrutura de dados da aplicação foi pensada para ser clara, o que facilitaria essa troca de informações. Por exemplo, os dados de usuários e suas permissões (definidos em arquivos como `seed_usuarios.js` e `seed_funcoes_usuario.js`) poderiam, no futuro, ser sincronizados com um sistema de login central do IPT, caso exista.

*   **Preparada para Crescer (Escalabilidade):**
    A aplicação está sendo construída com tecnologias (Node.js) que são conhecidas por sua capacidade de lidar com um número crescente de usuários e um volume maior de dados. A organização do banco de dados foi planejada para suportar o aumento de inspeções, fotos e informações sem perder desempenho. Se, no futuro, a demanda crescer muito, a infraestrutura onde a aplicação e o banco de dados rodam poderá ser ajustada para maior capacidade, garantindo que o sistema continue rápido e estável.

*   **Cuidados e Melhorias Contínuas (Manutenção):**
    Como qualquer sistema, após a entrega, esta aplicação precisará de atenção contínua. Isso inclui atualizações de segurança, correção de eventuais problemas que surjam com o uso e, possivelmente, o desenvolvimento de novas funcionalidades que o IPT identifique como necessárias. Para facilitar esse trabalho, o projeto será documentado. O uso de GitHub para gerenciar o código do projeto, também ajudará na organização das manutenções e futuras versões. A equipe do IPT ou outros desenvolvedores poderão, assim, entender como a aplicação funciona para realizar essas melhorias.

**4. Forma de utilização da solução**
A aplicação será usada por inspetores e gestores para realizar inspeções prediais. O usuário acessa via navegador, inicia uma nova inspeção, registra dados e fotos no local, associa aos pontos do prédio e finaliza com geração automática de relatório técnico. Os dados ficam salvos, podem ser exportados e consultados depois, com histórico acessível e organizado.

**5. Benefícios esperados**
O projeto trará benefícios práticos e estratégicos ao IPT, como padronização das inspeções, geração ágil de relatórios e melhoria na qualidade dos dados. A ferramenta aumenta a rastreabilidade, organiza um histórico confiável e reforça o IPT como referência no setor. Com sucesso, pode ser adotada por outros parceiros, ampliando o impacto e abrindo novas oportunidades.

**6. Critério de sucesso e como será avaliado**
O sucesso será medido por: funcionamento em múltiplos dispositivos; registro confiável de dados; geração automática de relatórios técnicos; segurança e anonimização das informações; busca eficiente e rastreabilidade dos usuários; e validação prática pelos profissionais do IPT. Avaliações incluirão testes reais, feedback dos usuários, checagem de compatibilidade, integridade dos dados e conformidade com privacidade.

### 2.1.4. Value Proposition Canvas (sprint 1):

O Canvas de Proposta de Valor é uma ferramenta estratégica que ajuda a alinhar os produtos e serviços de uma empresa com as necessidades e desejos do cliente. Esse modelo visual é utilizado para compreender melhor como uma proposta de valor pode gerar benefícios e resolver as "dores" do público-alvo.

O canvas é dividido em dois grandes blocos: o Perfil do Cliente e o Mapa de Valor. No perfil do cliente, são analisados os trabalhos que ele precisa realizar, suas dores (problemas, obstáculos ou riscos) e ganhos (benefícios esperados ou desejados). Já no mapa de valor, são definidos os produtos e serviços oferecidos, os aliviadores de dor (como a proposta reduz os problemas do cliente) e os criadores de ganho (como ela gera benefícios adicionais).

A combinação dessas duas partes permite que se construa uma proposta de valor direcionada e eficaz. A seguir, o canvas desenvolvido para este projeto:


<img src="../assets/canvas.png">

### 2.1.5. Matriz de Riscos do Projeto (sprint 1)

&emsp; ​A matriz de risco é uma ferramenta essencial na gestão empresarial, utilizada para identificar, avaliar e priorizar os riscos que podem impactar as operações de uma organização. Ela cruza dois fatores principais: a probabilidade de ocorrência de um evento e a gravidade de suas consequências. Por meio dessa análise, é possível visualizar de forma clara quais riscos exigem atenção imediata e quais podem ser monitorados com menor urgência, facilitando a tomada de decisões estratégicas e a implementação de medidas preventivas eficazes. Essa abordagem sistemática contribui para a minimização de perdas e a promoção da segurança e eficiência nos processos empresariais[₃](#ref-matrizRisco).

<img src="../assets/Matriz Risco.png">

## Ameaças (Riscos)

### Erro técnico na aplicação
- Probabilidade: 70
- Impacto: Alto
- Explicação: Bugs graves ou falhas no funcionamento podem comprometer toda o projeto.
- **Plano de Ação:**
    - Prevenção: Implementar testes abrangentes (unitários, integração); realizar revisões de código.
    - Mitigação/Contingência: Manter controle de versão (Git) para rollback; ter processo ágil de hotfix.

### Atraso na entrega final
- Probabilidade: 70
- Impacto: Muito Alto
- Explicação: Atrasos podem prejudicar o cronograma do módulo e o uso real da aplicação para diagnósticos prediais.
- **Plano de Ação:**
    - Prevenção: Planejar sprints com tarefas menores e realistas; comunicação constante com o IPT.
    - Mitigação/Contingência: Focar no MVP; ter um buffer de tempo no cronograma; renegociar escopo se necessário.

### Conteúdo não aprovado pela empresa
- Probabilidade: 50
- Impacto: Moderado
- Explicação: Se as informações, textos ou funcionalidades não estiverem alinhadas com o padrão do IPT, será necessário refazer partes do projeto, gerando retrabalho.
- **Plano de Ação:**
    - Prevenção: Validações frequentes com o IPT (sprint reviews); documentar requisitos e obter aprovação.
    - Mitigação/Contingência: Desenvolver de forma modular; manter backlog flexível para feedback.

### Ferramenta não funcional
- Probabilidade: 30
- Impacto: Moderado
- Explicação: Ter uma funcionalidade no sistema que não executa o que promete pode causar desconfiança no uso da aplicação web e prejudicar a experiência de inspetores.
- **Plano de Ação:**
    - Prevenção: Definir critérios de aceite claros; realizar testes funcionais e de usabilidade com o IPT.
    - Mitigação/Contingência: Canal ágil para reporte de bugs; priorizar correções críticas.

### Pitch ruim
- Probabilidade: 30
- Impacto: Alto
- Explicação: Um pitch mal estruturado pode impedir que o valor da aplicação seja bem compreendido pelo IPT e pelos avaliadores.
- **Plano de Ação:**
    - Prevenção: Preparar roteiro focado nos benefícios; ensaiar a apresentação; criar slides claros.
    - Mitigação/Contingência: Ter material de apoio (WAD); preparar-se para perguntas.

### Problema com o banco de dados
- Probabilidade: 30
- Impacto: Muito Alto
- Explicação: A aplicação depende do banco para armazenar dados de inspeções. Falhas nessa área podem inviabilizar o uso do sistema.
- **Plano de Ação:**
    - Prevenção: Design cuidadoso do BD (ver `diagramaDB.png`); usar migrations (`src/migrations`); backups regulares.
    - Mitigação/Contingência: Plano de recuperação de desastres testado; usar transações ACID.

### Paleta de cores mal escolhida
- Probabilidade: 10
- Impacto: Muito Baixo
- Explicação: Cores mal aplicadas podem dificultar a leitura de relatórios e a usabilidade.
- **Plano de Ação:**
    - Prevenção: Basear em princípios de UI e acessibilidade; validar com usuários; documentar no Guia de Estilos.
    - Mitigação/Contingência: Usar variáveis CSS para fácil alteração da paleta.

### Interface visual inconsistente
- Probabilidade: 10
- Impacto: Baixo
- Explicação: Layouts desalinhados ou variações visuais entre páginas reduzem a fluidez do uso pela equipe técnica do IPT.
- **Plano de Ação:**
    - Prevenção: Criar Guia de Estilos e componentes reutilizáveis; revisões de design.
    - Mitigação/Contingência: Auditorias de UI periódicas para identificar e corrigir inconsistências.

### Desalinhamento com a cultura da empresa
- Probabilidade: 10
- Impacto: Moderado
- Explicação: Se o design, linguagem ou abordagem do app destoarem do perfil técnico e institucional do IPT, a adoção pode ser comprometida.
- **Plano de Ação:**
    - Prevenção: Entender a cultura do IPT; envolver usuários no design; usar linguagem técnica e objetiva.
    - Mitigação/Contingência: Coletar feedback pós-lançamento e realizar ajustes.

## Oportunidades

### Reorganizar o cronograma e melhorar a gestão de tempo
- Probabilidade: 70
- Impacto: Muito Alto
- Explicação: Otimizar o tempo interno da equipe ajuda a lidar com imprevistos e garante uma entrega mais sólida e testada.

### Melhorar a base do código e evitar retrabalho
- Probabilidade: 70
- Impacto: Alto
- Explicação: Refatorar o código enquanto há tempo reduz chances de bugs futuros e facilita futuras integrações ou manutenções.

### Alinhar melhor com feedbacks da empresa
- Probabilidade: 50
- Impacto: Moderado
- Explicação: A aplicação será mais útil se responder diretamente às sugestões e dores identificadas pelo IPT durante o processo.

### Aprender a lidar com integrações e segurança de dados
- Probabilidade: 30
- Impacto: Muito Alto
- Explicação: Ao entender como proteger os dados e integrar a aplicação a sistemas futuros, o projeto se torna mais robusto e aplicável a contextos reais.

### Aprimorar a comunicação e a clareza no pitch
- Probabilidade: 30
- Impacto: Alto
- Explicação: Saber comunicar com precisão os benefícios do sistema facilita a aprovação pela empresa.

### Priorizar funcionalidades úteis
- Probabilidade: 30
- Impacto: Moderado
- Explicação: Focar nas ferramentas mais relevantes para os técnicos do IPT aumenta a efetividade prática da solução.

### Conhecer melhor a empresa e adaptar o projeto ao contexto dela
- Probabilidade: 10
- Impacto: Moderado
- Explicação: Entender o dia a dia das inspeções prediais permite um projeto mais direcionado e realista.

### Definir um padrão visual e melhorar a experiência do usuário
- Probabilidade: 10
- Impacto: Baixo
- Explicação: Um design coeso, limpo e técnico melhora a navegabilidade e facilita o uso da aplicação por equipes variadas do IPT.

### Testar novas paletas e fortalecer a identidade visual
- Probabilidade: 10
- Impacto: Muito Baixo
- Explicação: Experimentar combinações visuais pode tornar o sistema mais agradável sem comprometer a funcionalidade.




## 2.2. Personas (sprint 1)

A criação de personas é utilizada para representar os usuários-alvo de uma aplicação de forma mais concreta e humanizada. Por meio da construção de personagens fictícios baseados em características reais do público, é possível compreender melhor suas necessidades, comportamentos, dores e objetivos. Essa representação ajuda na desenvolvimento de soluções mais alinhadas às expectativas e demandas dos usuários, garantindo maior usabilidade e eficiência do sistema. 

<div align = 'center'>
<img src = '../assets/persona-claudia.png'>
<img src = '../assets/persona-andre.png'>
</div>

## 2.3. User Stories (sprints 1 a 5)

As user stories são descrições breves e simples que capturam as necessidades ou desejos dos usuários em relação ao sistema. Elas são formuladas do ponto de vista do próprio usuário e ajudam a guiar o desenvolvimento das funcionalidades de forma focada nas experiências reais. Cada user story geralmente inclui um critério de aceitação, que define as condições que a funcionalidade deve atender para ser considerada concluída. Esse método facilita a comunicação entre as equipes de design, desenvolvimento e stakeholders, promovendo um produto mais centrado no usuário.

Identificação | US01  
--- | ---  
Persona | André Moura  
User Story | "Como engenheiro de obras, posso capturar fotos diretamente da inspeção pelo tablet, para organizar os registros visuais de maneira ágil e integrada ao ambiente inspecionado."  
Critério de aceite 1 | O sistema deve permitir o acesso à câmera do dispositivo. Teste: O botão "capturar foto" deve abrir a câmera do tablet.  
Critério de aceite 2 | O usuário deve conseguir vincular a foto a um ambiente ou item da inspeção. Teste: Após tirar a foto, o sistema deve solicitar o local associado.  
Critério de aceite 3 | A imagem deve ser salva no banco de dados junto com metadados. Teste: Ao consultar a inspeção, a imagem deve aparecer com data, hora e ambiente.  
Critérios INVEST | Independente: não depende de outras funcionalidades. Negociável: pode-se ajustar a resolução das fotos. Valorosa: agiliza e organiza o processo. Estimável: escopo claro e mensurável. Pequena: foca apenas na captura e salvamento. Testável: é possível testar funcionalmente em qualquer inspeção.

Identificação | US02  
--- | ---  
Persona | André Moura  
User Story | "Como responsável técnico, posso gerar relatórios automaticamente a partir dos dados coletados, para reduzir o tempo de elaboração e padronizar os documentos entregues."  
Critério de aceite 1 | O sistema deve consolidar dados e imagens em um modelo de relatório. Teste: Após finalização da inspeção, o relatório gerado deve conter todas as entradas.  
Critério de aceite 2 | O relatório deve ser exportável em PDF. Teste: Botão "Exportar PDF" gera um arquivo com as informações da inspeção.  
Critério de aceite 3 | O relatório deve ter cabeçalho com identificação da inspeção. Teste: Arquivo PDF inicia com nome do prédio, data e inspetor.  
Critérios INVEST | Independente: não requer dados externos. Negociável: layout do relatório pode ser ajustado. Valorosa: reduz retrabalho e melhora entrega. Estimável: baseada em templates existentes. Pequena: foca na geração e exportação. Testável: PDF gerado pode ser comparado com dados da inspeção.

Identificação | US03  
--- | ---  
Persona | André Moura  
User Story | "Como inspetor de edificações, posso comparar inspeções anteriores com as atuais, para analisar a evolução de problemas estruturais com mais precisão."  
Critério de aceite 1 | Sistema deve listar inspeções anteriores do mesmo local. Teste: Ao acessar um prédio, lista com datas das inspeções aparece.  
Critério de aceite 2 | Deve ser possível visualizar comparações entre inspeções. Teste: Interface mostra imagens e dados antigos e atuais lado a lado.  
Critério de aceite 3 | Deve indicar itens recorrentes ou agravados. Teste: Sistema destaca em vermelho itens que pioraram.  
Critérios INVEST | Independente: utiliza base de dados já registrada. Negociável: modo de visualização pode variar. Valorosa: fortalece decisões técnicas. Estimável: função delimitada e clara. Pequena: foca só na comparação. Testável: simulação com dados de inspeções anteriores.

Identificação | US04  
--- | ---  
Persona | André Moura  
User Story | "Como usuário em campo, posso registrar itens diretamente sobre um modelo digital da edificação, para localizar facilmente os problemas nos diferentes ambientes."  
Critério de aceite 1 | Interface com modelo clicável por ambientes. Teste: Clicar em um cômodo abre campo para anotação e imagem.  
Critério de aceite 2 | Dados devem ser salvos com localização associada. Teste: Revisão da inspeção mostra onde cada item foi registrado.  
Critério de aceite 3 | Deve ser possível adicionar múltiplos registros por ambiente. Teste: Sistema aceita mais de um apontamento por local.  
Critérios INVEST | Independente: requer apenas modelo da edificação. Negociável: tipos de ambientes ou detalhamento podem variar. Valorosa: melhora rastreabilidade. Estimável: depende da complexidade do modelo. Pequena: operação local do registro. Testável: visualização e vínculo de dados ao modelo.

Identificação | US05  
--- | ---  
Persona | André Moura  
User Story | "Como profissional técnico, posso acessar dados centralizados e estruturados, para facilitar futuras consultas e revisões com outros membros da equipe."  
Critério de aceite 1 | Sistema deve permitir filtros por data, local e tipo de dano. Teste: Aplicação exibe resultados corretos ao usar filtros.  
Critério de aceite 2 | Deve ser possível exportar os dados em .csv. Teste: Botão de exportação gera arquivo com dados filtrados.  
Critério de aceite 3 | Estrutura hierárquica de dados deve ser visível. Teste: Interface mostra prédio > ambientes > registros.  
Critérios INVEST | Independente: parte apenas da base de dados. Negociável: critérios de filtro podem ser ampliados. Valorosa: agiliza análise e revisão. Estimável: tarefas bem definidas. Pequena: foco apenas em organização e exportação. Testável: teste direto com banco de dados populado.

Identificação | US06  
--- | ---  
Persona | Cláudia Barbosa  
User Story | "Como arquiteta responsável por retrofit, posso associar fotos e anotações a elementos específicos do modelo digital do edifício, para facilitar a visualização das manifestações patológicas e orientar melhor as intervenções."  
Critério de aceite 1 | Modelo digital com pontos interativos. Teste: Clique em componente permite adicionar foto e nota.  
Critério de aceite 2 | Dados devem ser armazenados com referência visual clara. Teste: Ao acessar item no relatório, imagem e texto aparecem com marcação no modelo.  
Critério de aceite 3 | Vários elementos devem aceitar registros simultâneos. Teste: Interface permite anotações em múltiplos pontos.  
Critérios INVEST | Independente: opera sobre função visual do sistema. Negociável: pode usar modelos 2D inicialmente. Valorosa: contribui para diagnóstico técnico. Estimável: pode ser mensurado por componentes. Pequena: escopo definido (associação de dados). Testável: simulação em ambiente teste.

Identificação | US07  
--- | ---  
Persona | Cláudia Barbosa  
User Story | "Como profissional que elabora relatórios técnicos, posso gerar documentos visuais e bem organizados diretamente pela plataforma, para apresentar diagnósticos claros a engenheiros e clientes não técnicos."  
Critério de aceite 1 | Relatório deve incluir texto, imagem e croqui. Teste: PDF gerado contém os três elementos com clareza.  
Critério de aceite 2 | Deve permitir inclusão de logotipo e observações. Teste: Sistema aceita upload de logotipo e campo adicional.  
Critério de aceite 3 | O layout deve seguir um modelo técnico pré-definido. Teste: Estrutura do documento segue padrão conforme template.  
Critérios INVEST | Independente: foco no exportador de relatórios. Negociável: layout e formatação. Valorosa: melhora comunicação com stakeholders. Estimável: baseado em estrutura existente. Pequena: exportação e layout. Testável: geração do PDF.

Identificação | US08  
--- | ---  
Persona | Cláudia Barbosa  
User Story | "Como arquiteta que acompanha a evolução de problemas, posso consultar e comparar inspeções anteriores em uma linha do tempo, para justificar a necessidade de manutenção corretiva ou preventiva."  
Critério de aceite 1 | Sistema apresenta inspeções passadas por ordem cronológica. Teste: Linha do tempo mostra datas e dados resumidos.  
Critério de aceite 2 | Itens recorrentes são destacados. Teste: Sistema usa cor ou ícone para apontar reincidência.  
Critério de aceite 3 | Deve permitir exportar a comparação. Teste: Comparativo pode ser gerado em PDF.  
Critérios INVEST | Independente: usa dados existentes. Negociável: tipos de visualização. Valorosa: fundamenta decisões. Estimável: baseada em dados temporais. Pequena: foco na visualização comparativa. Testável: testes com base populada.

Identificação | US09  
--- | ---  
Persona | Cláudia Barbosa  
User Story | "Como usuária em campo, posso registrar observações diretamente via tablet, com interface fluida e responsiva, para ganhar agilidade e não depender de anotações manuais ou retrabalho no escritório."  
Critério de aceite 1 | Interface adaptada para toque e responsiva. Teste: Em tablet, sistema deve ajustar visual.  
Critério de aceite 2 | Campos de texto, imagem e checklist acessíveis. Teste: Registro de item funciona em ambiente mobile.  
Critério de aceite 3 | Dados sincronizados com o servidor. Teste: Registro local aparece no sistema web após salvar.  
Critérios INVEST | Independente: atua no front-end mobile. Negociável: campos usados. Valorosa: economiza tempo em campo. Estimável: baseado em adaptação responsiva. Pequena: escopo bem delimitado. Testável: testes manuais em tablet.

Identificação | US10  
--- | ---  
Persona | Cláudia Barbosa  
User Story | "Como arquiteta envolvida em múltiplos projetos, posso organizar e filtrar as inspeções por edifício, sistema construtivo ou tipo de dano, para manter o controle e histórico técnico de cada obra com precisão."  
Critério de aceite 1 | Filtros múltiplos disponíveis na interface. Teste: Combinação de filtros retorna lista precisa.  
Critério de aceite 2 | Resultados organizados hierarquicamente. Teste: Lista mostra Edifício > Sistema > Item.  
Critério de aceite 3 | Possibilidade de exportação dos resultados. Teste: Botão "Exportar dados filtrados" gera CSV.  
Critérios INVEST | Independente: atua sobre dados existentes. Negociável: critérios de filtro. Valorosa: melhora organização e análise. Estimável: funcionalidade comum. Pequena: implementação pontual. Testável: testes com diferentes filtros.

# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

## 3.1. Arquitetura (sprints 3 e 4)

A arquitetura de modelo de dados define a estrutura e os relacionamentos entre as entidades do sistema, orientando como os dados são armazenados e acessados. Neste projeto, ela é essencial pois permite o controle estruturado de dados como os de usuários, equipes, etapas da inspeção e patologias, além de facilitar a manutenção e evolução do sistema a longo prazo.<br>
Para isso, a arquitetura - representada pelo diagrama abaixo - foi estruturada de acordo com o padrão MVC (Model-View-Controller), com separação entre o frontend (visualização), o backend (lógica de negócio e controles) e o banco de dados.

### Model

A camada Model é responsável por definir a estrutura das tabelas no banco de dados, os atributos de cada entidade e os relacionamentos entre elas. Para este projeto, as entidades são:

**usuários**: Armazena informações básicas dos usuários (nome, e-mail, senha).<br>
**funcoesUsuarios**: Relaciona usuários com seus cargos.<br>
**equipesInspecao**: Define a composição das equipes responsáveis pelas inspeções e as funções de cada um nela.<br>
**inspecoes**: Tabela principal que registra os dados das inspeções prediais.<br>
**relatorios**: Associados às inspeções, contêm o status e o responsável pela geração do relatório final.<br>
**edificios**: Relacionados às inspeções, armazenam informações do prédio.<br>
**pavimentos**: Associados aos edifícios, representam os andares.<br>
**Ambientes**: Ligados aos pavimentos, representam os espaços físicos - como quarto e cozinha.<br>
**sistemas**: Relacionados aos ambientes, descrevem os sistemas inspecionados.<br>
**patologias**: Associadas aos sistemas, descrevem anormalidades encontradas na inspeção.<br>
**fotos**: Ligadas às patologias, armazenam evidências visuais com legenda e autor.<br>

### Controllers

Os controladores fazem a ponte entre a View e os Models, permitindo a manipulação dos dados através de operações CRUD (Create, Read, Update, Delete) para cada entidade do sistema através de requisições HTML. Nesse projeto, cada tabela do banco possui um conjunto de métodos no controller que permitem buscar todos os registros, buscar por ID, criar novos registros, atualizar e deletar.

### View

Refere-se a interface do usuário, ou seja, como o usuário interagem com a aplicação web. Existem, portanto, três sessões principais:

**Login**: Tela de autenticação com opções para login, redefinição de senha e criação de conta.<br>
**Painel ADM**: Painel administrativo com funcionalidades para criação de inspeções, conferir estatísticas e relatórios.<br>
**Painel Coordenador/Inspector**: Telas com acesso para coordenadores e inspetores, com foco em visualização e edição de inspeções e relatórios.<br><br>


Para ver a imagem com maior qualidade, [clique aqui](https://drive.google.com/file/d/1BjdBHQwtZGl6WnMDekP0vv8eyosx8Yka/view?usp=sharing).

<div align="center">
<img src="../assets/arquitetura.png">
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

## 3.2. Wireframes (sprint 2)


Wireframes são representações visuais simplificadas de uma interface, e foram utilizadas para planejar o layout e a organização dos elementos da nossa aplicação web durante a fase inicial de desenvolvimento. As imagens a seguir são wireframes de baixa fidelidade, ou seja, versões que focam na estrutura básica e funcionalidade, e são importantes para validar fluxos de navegação e informações importantes de cada sessão, por exemplo.  

O conjunto de telas foi dividito em 4 sessões principais: (1) Telas de Login; (2) telas dos administradores; (3) telas dos coordenadores; e (4) telas dos inspetores.

<div align="center">
<img src = '../assets/login.png'><br><br>
<sup> Figura 1: Wireframe Desktop - telas de login: (0) login; (1) redefinir senha; e (2) cadastro.</sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>
  
A tela de login é a página inicial que todo usuário vê ao acessar o site. Por meio dela, também é possível ser direcionado  para a tela de redefinir senha e de cadastro. Todas as telas contam com campos para preenchimento das informações mais importantes para cada finalidade. Vale destacar também que, para o login ser validado, todo membro da equipe deverá realizar seu cadastro diretamente pelo site. A partir desse cadastro os membros são identificados pelos seus cargos e, ao realizar o login, serão direcionados para as telas adequadas.

<div align="center">
<img src = '../assets/administrador.png'><br><br>
<sup>Figura 2: Wireframe Desktop - telas exclusivas do administrador: (3) Menu expandido com os atalhos para navegabilidade (exemplificação de como ficaria ele expandido); (4) Tela inicial com a lista de inspeções; (5) Tela do relatório em pdf; (6) Informações da inspeção; (7) Edição/criação da inspeção; e (8) Lista completa de membros cadastrados.  </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br><br>

Caso o usuário seja um administrador, ao realizar o login, ele será direcionado para a tela inicial onde haverá uma lista com todo o histórico de inspeções. Ao clicar em "criar nova inspeção" será possível cadastrar uma inspeção no sistema e adicionar informações importantes sobre a cada uma e formar equipes. Após isso, o administrador poderá conferir essas informações novamente ao clicar no card da inspeção. Na página que se abre, é possível acessar o documento do relatório, bem como optar por editar as insformações. Por fim, da tela inicial o usuário pode conferir uma lista de membros cadastrados no sistema, seus cargos e a quantidade de inspeções a que já estão atribuídos. <br>

Essa sessão, portanto, está focada nas funções referentes a criação e edição de inspeções bem como atribuição de equipes para cada uma. 

<div align="center">
<img src = '../assets/coordenador.png'><br><br>
<sup>Figura 3 - Wireframe Desktop - telas exclusivas do coordenador: (9) menu com os atalhos para navegabilidade (exemplificação de como ficaria ele expandido); (10) tela inicial com a lista de inspeções; (11) solicitação para composição de equipe; (12) histórico das solicitações.</sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Caso o usuário seja um administrador, ele será direcionado para a tela inicial com a lista de inspeções. No menu, ao clicar em "solicitação", será possível ver uma lista de solicitações e o status de cada uma, além de criar novas. Tais solicitações são um pedido relacionado a composição de equipe que ele gostaria de ter para determinada inspeção. <br>
Como o coordenador também realiza as inspeções, ele também tem acesso às telas referentes ao formulário - onde são colocadas as informações das inspeções -, e, por isso, compartilha algumas das telas que estão ilustradas na figura 4. O formulário é, portanto, acessado ao clicar no card de determinada inspeção.

<div align="center">
<<img src = '../assets/inspetor.png'><br><br>
<sup> Figura 4: Wireframe Desktop - telas do inspetor e coordenador: (13) tela inicial com a lista de inspeções; (14) lista de membros que compõem a equipe de uma inspeção em espefíco; (15) informações da construção; (16) menu com os atalhos para navegabilidade (exemplificação de como ficaria ele expandido); (17) listagem de edifícios da inspeção; (18) listagem de ambientes; (19) listagem de sistemas e patologias; (20) detalhamento das patologias; (21) edição ou cadastramento das patologias; e (22) tela de edição de imagens. </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Caso o usuário seja um inspetor, ele será direcionado à tela inicial com a lista de inspeções. A partir dessa interface, é possível acessar os detalhes de cada inspeção por meio dos seus respectivos cards. Ao selecionar uma inspeção, o usuário poderá visualizar a equipe envolvida, informações específicas da obra e o formulário de inspeção.

Tanto inspetores quanto coordenadores associados a uma mesma inspeção têm permissão para editar o formulário. No entanto, coordenadores possuem funcionalidades exclusivas, como o botão "Gerar Relatório". A navegação, a partir de então, se desenvolve de forma hierárquica: ao clicar em um card de inspeção, o usuário é gradualmente direcionado a níveis mais específicos, até chegar às patologias e suas respectivas imagens. Cada card de patologia permite a leitura e edição das informações associadas.

Por fim, as imagens anexadas às patologias podem ser editadas com o objetivo de facilitar a identificação precisa dos problemas. As ferramentas disponíveis incluem: inserção de figuras geométricas, seleção de cores, corte, restauração de alterações, exclusão e ajuste de zoom. Também é possível adicionar marcadores visuais e legendas para contextualizar as anomalias registradas.

Dessa forma, atendemos uma série de user stories citadas anteriormente. Dentre elas, vale destacar as user storys: **US01 (captura de fotos)** - atendida na tela 21 por meio do botão "adicionar" na parte de imagem; **US02 (geração de relatórios)** - atendida na tela 17 por meio do botão "gerar relatório" que é visível apenas para o coordenador da inspeção e fica disponível após as informações de todos os campos estarem corretamente preenchidas; **US04 (registro de ítens da inspeção)** - atendida pelo conjunto de telas de 17 a 22; e **US10 (filtro para as inspeções)** - atendida na tela 13 por meio da barra de pesquisa, onde seria possível colocar datas e palavras chaves, por exemplo, e filtrar a partir disso.

[clique aqui](https://drive.google.com/drive/folders/1fPHPQH4N5Y1LJWy0IApVC7fenx5Tnwvb?usp=sharing) para acessar as imagens como arquivo.

## 3.3. Guia de estilos (sprint 3)

O guia de estilos do projeto é uma biblioteca visual que reúne componentes reutilizáveis e diretrizes de design, facilitando o desenvolvimento e garantindo consistência na interface. Ele foi elaborado com base na identidade visual do IPT, mas foi incrementado por uma interface personalizada do nosso projeto, utilizando novos fundamentos como cores, tipografia e iconografia.

Todos os componentes foram organizados em um Design System no Figma, servindo como referência central para a equipe. O sistema contribui para a padronização, agilidade no desenvolvimento e alinhamento com boas práticas de usabilidade.

O acesso ao guia completo pode ser feito pela plataforma Figma clicando [aqui](https://www.figma.com/design/gAYZb14vuuMoHoDJF4Ga42/Guia-de-Estilos---Nexgrid?node-id=0-1&p=f&t=WZgfo2Ot6kzB1J8Z-0) 

### 3.3.1 Cores

A paleta de cores em um projeto web representa o conjunto de tonalidades escolhidas para compor a identidade visual e a estética geral da aplicação. Ela costuma ser composta por cores primárias, neutras e secundárias, selecionadas com base em princípios de design, acessibilidade e harmonia visual.

No contexto deste projeto, o parceiro IPT nos concedeu liberdade para definir nossa própria identidade visual. Isso permitiu à equipe criar uma paleta exclusiva, alinhada aos objetivos do sistema e à experiência que desejamos proporcionar aos usuários. A escolha cuidadosa das cores contribui para uma navegação mais clara, agradável e intuitiva, influenciando diretamente a percepção e o engajamento dos usuários com a aplicação.

Decidimos então, deixar como Paleta Primária a base da identidade visual do IPT, marcada por tons de azul que unificam os elementos interativos e reforçam a presença da marca. 

<div align="center">
<<img src = '../assets/cores1.png'><br>
<sup> Figura 1 - Cores Primárias </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

A Paleta Neutra oferece uma variedade de tons sutis, ideais para fundos, elementos de texto e divisores. Elas são escolhidas para adicionar profundidade e contraste à interface, garantindo legibilidade e coesão visual.

<div align="center">
<<img src = '../assets/neutra.png'><br>
<sup> Figura 2 - Paleta neutra </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

A Paleta Secundária desempenha um papel na orientação do usuário, identificação de status, compreensão de ações e localização de ajuda. Ela funciona transmitindo uma ação de alto impacto e garantindo clareza na comunicação visual.

<div align="center">
<<img src = '../assets/cores2.png'><br>
<sup> Figura 3 - Paleta Secundária </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

### 3.3.2 Tipografia

A tipografia afeta diretamente como o conteúdo é lido, entendido e absorvido pelos usuários. Escolher as fontes certas, seus tamanhos e espaçamentos garante uma experiência de leitura agradável e eficaz. Além disso, a tipografia ajuda a transmitir a identidade da marca, definindo seu estilo e personalidade. Quando usada com sabedoria, a tipografia pode melhorar significativamente a experiência do usuário e reforçar a mensagem da marca.

 Assim, no contexto do projeto, as fontes selecionadas foram Public Sans, Noto Sans e sans-serif. Elas foram escolhidas por serem opções versáteis que oferecem diferentes variações adequadas para diferentes elementos. Essas fontes são uma escolha popular devido à sua legibilidade e clareza em telas digitais, além de possuirem uma ampla gama de pesos e estilos, permitindo uma hierarquia visual consistente e atraente. Para os títulos, optou-se por uma variação maior e destacada, para os subtítulos, foi escolhida uma variação intermediária, enquanto para o texto corrido, foi selecionada uma variação mais leve e fácil de ler.

<div align="center">
<<img src = '../assets/tipografia.png'><br>
<sup> Figura 4 - Tipografia </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

### 3.3.3 Iconografia e imagens

Os ícones são elementos visuais compactos e de fácil reconhecimento que desempenham um papel fundamental na comunicação dentro da interface. Eles transmitem informações de forma rápida, direta e intuitiva, contribuindo para uma navegação mais fluida e acessível, especialmente quando utilizados de forma consistente.

No projeto, os ícones foram cuidadosamente escolhidos para representar ações, categorias, status e funcionalidades específicas. Além disso, sua aplicação reforça o significado de botões e menus, reduzindo a necessidade de texto excessivo e melhorando a experiência do usuário, inclusive para públicos com diferentes níveis de letramento digital.

<div align="center">
<<img src = '../assets/icone.png'><br>
<sup> Figura 5 - Iconografia </sup></br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

## 3.4 Protótipo de alta fidelidade (Sprint 3)

Um protótipo de alta fidelidade é uma simulação visual e funcional do sistema, próxima da versão final, usada para validar o design, testar a usabilidade e garantir o alinhamento com as necessidades dos usuários. Este protótipo foi desenvolvido com base nas User Stories mapeadas na Sprint 1 e estruturado com os componentes definidos no Guia de Estilos.

 **Clique [aqui](https://www.figma.com/design/z22EwTcMyzbfEPUUeiQfrw/nexgrid?node-id=0-1&t=dWYSko1mINn7xcZm-1.) para acessar o protótipo completo no Figma.**

---

### 3.4.1 Telas de Acesso (Login, Cadastro e Recuperação de Senha)

As telas de acesso são responsáveis por garantir que apenas usuários autorizados possam utilizar o sistema. Elas incluem login, cadastro de novos usuários e recuperação de senha.

---

#### Tela de Login

<div align="center">
<img src="../assets/prototipologin.png"><br>
<sup>Figura 1 – Tela "Login", com campos de autenticação e redirecionamento.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

A tela de login permite que o usuário acesse a plataforma informando e-mail e senha. Também há links para cadastro e recuperação de senha. O botão “Entrar” realiza a autenticação. Essa interface foi projetada com foco em clareza, simplicidade e responsividade, garantindo fácil uso tanto por inspetores quanto por administradores.

---

#### Tela de Cadastro

<div align="center">
<img src="../assets/cadastro.png"><br>
<sup>Figura 2 – Tela "Cadastro", com campos para criação de nova conta.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Na tela de cadastro, o usuário preenche nome, sobrenome, e-mail e senha (com confirmação) para criar uma conta. O botão “Cadastrar” finaliza o processo, e há também um link para retornar à página de login. A hierarquia visual e o espaçamento dos elementos ajudam a evitar erros durante o preenchimento.

---

#### Tela de Recuperação de Senha

<div align="center">
<img src="../assets/recuperarsenha.png"><br>
<sup>Figura 3 – Tela "Recuperação de Senha", com campo de e-mail para redefinição.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

A tela de recuperação de senha permite solicitar, por e-mail, a redefinição da senha de acesso. Após preencher o campo, o usuário recebe um link no e-mail cadastrado para criação de uma nova senha. A interface foi pensada para ser rápida, limpa e sem distrações, ideal para situações emergenciais.

---

Em seguida, apresentamos as telas destinadas ao perfil Inspetor, com foco nas funcionalidades de registro técnico, inspeção de ambientes e geração de relatórios.


### 3.4.2 Telas do Inspetor

O perfil do inspetor concentra as funcionalidades relacionadas ao registro técnico das inspeções, à visualização de histórico e à geração de relatórios técnicos. As telas a seguir foram desenvolvidas para garantir agilidade, clareza e rastreabilidade durante a atuação em campo. Todas estão conectadas diretamente às User Stories priorizadas para esse perfil.

---

#### Tela: Minhas Inspeções

<div align="center">
<img src="../assets/lista inspeções.png"><br>
<sup>Figura 1 – Tela "Minhas Inspeções", com filtros por status, data e ações.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Essa tela permite ao inspetor consultar as inspeções atribuídas a ele, visualizando o status de cada uma (agendada, em andamento, concluída), bem como o endereço e datas. A funcionalidade de busca e filtros facilita a navegação. Esta tela está conectada à US03, pois apoia a comparação de inspeções anteriores e atuais, além de melhorar o acesso ao histórico técnico.

---

#### Tela: Realizando Inspeção

<div align="center">
<img src="../assets/iniciarinspeção.jpeg"><br>
<sup>Figura 2 – Tela "Realizando Inspeção", com dados gerais e gerenciamento de edifícios.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Durante a execução de uma inspeção, o sistema exibe os dados básicos da atividade, equipe envolvida e os edifícios relacionados. Essa tela serve como ponto de partida para o registro técnico em campo. Relaciona-se com a US01 e US04, pois permite organizar os registros visuais e técnicos por ambiente e prepara o caminho para vinculação com modelos digitais.

---

#### Tela: Detalhes da Inspeção

<div align="center">
<img src="../assets/detalhesinspeção.png"><br>
<sup>Figura 3 – Tela "Detalhes da Inspeção", com informações completas e estrutura do prédio.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Aqui são exibidos os dados cadastrais da inspeção, como ID, endereço, datas e equipe. O botão para acessar a estrutura detalhada permite registrar itens diretamente por ambiente. Relaciona-se com a US04, pois facilita o mapeamento visual e localização precisa de problemas no modelo digital.

---

#### Tela: Meu Perfil

<div align="center">
<img src="../assets/perfil.png"><br>
<sup>Figura 4 – Tela "Meu Perfil", com dados pessoais e opção de edição.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

A tela de perfil exibe os dados do inspetor logado, permitindo edições de e-mail, telefone e especialização. Embora não esteja vinculada diretamente a uma User Story funcional, ela oferece suporte à personalização da experiência do usuário e à confiabilidade das informações em relatórios.

---

#### Tela: Meus Relatórios

<div align="center">
<img src="../assets/relatorios.png"><br>
<sup>Figura 5 – Tela "Meus Relatórios", com filtros e acesso aos documentos gerados.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Nesta interface, o inspetor acessa relatórios técnicos gerados a partir das inspeções. É possível filtrar por tipo e período, visualizar detalhes e baixar os documentos. Relaciona-se com a US02 e US05, pois centraliza os dados estruturados, permitindo fácil consulta, exportação e revisão técnica.

---

#### Tela: Visualizar/Editar Relatório

<div align="center">
<img src="../assets/vizualizarrelatorio.png"><br>
<sup>Figura 6 – Tela "Editar Relatório", com campos preenchíveis e exportação em PDF.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Nesta tela, o inspetor pode preencher ou revisar um relatório técnico já iniciado, incluindo título, palavras-chave e sumário técnico. O botão "Salvar" e a opção de exportar em PDF garantem praticidade e padronização. Está ligada à US02 e US07, pois automatiza a geração de relatórios consistentes e visualmente adequados.

---

Na próxima seção, são apresentadas as telas do perfil Administrador, voltadas à gestão de usuários, ambientes e inspeções na plataforma.

### 3.4.3 Telas do Administrador

A seguir, apresentamos as telas destinadas ao perfil **Administrador**, responsável por coordenar inspeções, gerenciar membros da equipe e gerar relatórios a partir das informações coletadas no campo.

---

#### Tela \"Dashboard\"

<div align="center">
<img src="../assets/dashboard.png"><br>
<sup>Figura 1 – Tela \"Dashboard\", com resumo de inspeções em andamento, concluídas e agendadas.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Essa tela oferece ao administrador uma visão geral de todas as inspeções cadastradas, exibindo contagens organizadas por status. Essa funcionalidade está relacionada à US10 da Cláudia Barbosa, que exige controle e histórico técnico das obras com filtros e hierarquia.

---

#### Tela \"Detalhes da Inspeção\"

<div align="center">
<img src="../assets/detalhesinspeçãoadm.jpg"><br>
<sup>Figura 2 – Tela \"Detalhes da Inspeção\", com ambientes inspecionados e informações associadas.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Apresenta os dados da inspeção selecionada, como responsável, tipo de inspeção, e lista de ambientes. Essa tela auxilia no acesso centralizado e detalhado das inspeções (relacionada à US05).

---

#### Tela \"Detalhes do Ambiente\"

<div align="center">
<img src="../assets/detalhesambiente.png"><br>
<sup>Figura 3 – Tela \"Detalhes do Ambiente\", exibindo patologias registradas por sistema.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Esta tela mostra os dados coletados em cada ambiente inspecionado, incluindo patologias observadas e o sistema afetado. A organização por sistema facilita o vínculo com o modelo digital, contribuindo para a rastreabilidade conforme descrito na US04.

---

#### Tela \"Detalhes do Membro\"

<div align="center">
<img src="../assets/detalhesmembro.png"><br>
<sup>Figura 4 – Tela \"Detalhes do Membro\", com dados pessoais e inspeções atribuídas.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Permite ao administrador acompanhar os membros da equipe, visualizar as inspeções sob sua responsabilidade e atualizar dados conforme necessário. Esta funcionalidade ajuda na gestão eficiente de recursos humanos.

---

#### Tela \"Editar Inspeção\"

<div align="center">
<img src="../assets/editarinspeção.jpeg"><br>
<sup>Figura 5 – Tela \"Editar Inspeção\", com formulário para modificar dados da inspeção.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Facilita a alteração de dados de uma inspeção existente, permitindo ajustes rápidos em informações críticas como nome, responsável ou tipo de inspeção. Tal flexibilidade é importante para a manutenção da integridade dos dados no sistema.

---

#### Tela \"Membros\"

<div align="center">
<img src="../assets/membros.png"><br>
<sup>Figura 6 – Tela \"Membros\", listando profissionais, cargos, disponibilidade e inspeções atribuídas.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Com interface clara e filtros úteis, essa tela oferece uma visão geral da equipe de inspeção, permitindo ao administrador organizar e reatribuir responsabilidades com base na carga de trabalho e disponibilidade.

---

#### Tela \"Nova Inspeção\"

<div align="center">
<img src="../assets/novainspeção.png"><br>
<sup>Figura 7 – Tela \"Nova Inspeção\", com formulário completo de cadastro.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Tela utilizada para criar uma nova inspeção, contendo campos como nome, endereço, coordenador e datas. A criação organizada das inspeções é um ponto central para garantir rastreabilidade no sistema e atender a critérios das US01 e US02.

---

#### Tela \"Relatórios\"

<div align="center">
<img src="../assets/relatoriosadm.png"><br>
<sup>Figura 8 – Tela \"Relatórios\", com listagem e ações de acesso aos relatórios técnicos.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Esta tela organiza os relatórios gerados, permitindo filtragens por tipo e datas. Também há botões para visualização e download. Está diretamente relacionada às US02 e US07, que envolvem geração e exportação de relatórios com clareza e padrão.

---

#### Tela \"Visualizar Relatório\"

<div align="center">
<img src="../assets/vizualizarrelatorioadm.png"><br>
<sup>Figura 9 – Tela \"Visualizar Relatório\", com relatório técnico estruturado.</sup><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

Apresenta o relatório em formato técnico, com campos preenchidos automaticamente a partir dos dados coletados. Inclui botão para salvar como PDF, atendendo aos critérios das US02 e US07 com foco em automação e padronização documental.





## 3.5. Modelagem do banco de dados (sprints 2 e 4)

## 3.5.1. Modelo relacional (sprints 2 e 4)

### *******. Introdução

#### *******. Objetivo

Esta documentação tem como objetivo descrever detalhadamente o modelo de banco de dados desenvolvido.

#### *******. Escopo

Esta documentação abrange a estrutura completa do banco de dados relacional, incluindo todas as tabelas, relacionamentos, restrições e regras de validação necessárias.

#### *******. Definições, Acrônimos e Abreviações

- **PK**: Primary Key (Chave Primária)
- **FK**: Foreign Key (Chave Estrangeira)
- **ER**: Entity-Relationship (Entidade-Relacionamento)
- **SERIAL**: Tipo de dado auto-incrementável
- **VARCHAR**: Tipo de dado para armazenar strings de tamanho variável
- **TIMESTAMP**: Tipo de dado para armazenar data e hora
- **DATE**: Tipo de dado para armazenar data.
- **TEXT**: Tipo de dado para armazenar strings longas.
- **INTEGER**: Tipo de dado para armazenar números inteiros.
- **CASCADE**: Ação que propaga operações de exclusão ou atualização para registros relacionados.
- **SET NULL**: Ação que define o valor da chave estrangeira como NULL ao excluir ou atualizar o registro referenciado.

### *******. Visão Geral do Modelo de Dados

#### *******. Descrição Geral

O modelo de dados foi projetado para uma aplicação web que permite o registro estruturado de inspeções em edificações, documentação de manifestações patológicas com fotos e geração de relatórios técnicos. O sistema possui três níveis de permissão/acesso: inspetor, coordenador da inspeção e administrador, cada um com diferentes capacidades de acesso e edição.

A estrutura hierárquica do modelo segue o fluxo de uma inspeção predial: Inspeções são realizadas em Edifícios, que possuem Pavimentos. Em cada Pavimento, existem Ambientes, que contêm Sistemas (piso, parede, teto). Estes Sistemas podem apresentar Patologias, documentadas com Fotos. Os Relatórios são gerados a partir dos dados coletados nas Inspeções. Usuários são associados a Funções e podem fazer parte de Equipes de Inspeção.

#### *******. Diagrama do Modelo de Dados

O diagrama é uma representação visual da estrutura lógica de um banco de dados, sendo fundamental para a modelagem, normalização e implementação de sistemas. Nesse sentido, com a ajuda do diagrama, é possível entender as entidades (tabelas) envolvidas, seus atributos (colunas) e os relacionamentos entre elas, promovendo, assim, a integridade e coerência nos dados.<br>
No contexto deste projeto, o diagrama apresenta um total de 11 entidades (tabelas) conectadas entre si, cada uma com seus próprios atributos (colunas). Com essa estrutura, portanto, é possível garantir que todos os elementos do processo — como, por exemplo, usuários, inspeções, edifícios e patologias — estejam devidamente relacionados, o que assegura consistência, evita redundâncias, facilita o desenvolvimento e contribui para a manutenção e a evolução futura da aplicação.

[clique aqui](https://drive.google.com/file/d/1pCkl13Ec0YVpT7FtmBU1dp8jyLfN96cz/view?usp=sharing) para acessar as imagens com maior qualidade.


<div align="center">
<img src="../assets/diagramaDB.png"><br>
<sup>Fonte: Material produzido pelos autores (2025).</sup>
</div><br>

#### 3.5.2.4. Convenções de Nomenclatura

- **Tabelas**: Nomes no plural, em minúsculas (ex: usuarios, inspecoes). No WAD, para melhor leitura, usaremos maiúsculas (ex: USUARIOS, INSPECOES).
- **Colunas**: Nomes em minúsculas, usando underscores para separar palavras (ex: nome_edificio, criado_por).
- **Chaves Primárias**: Sempre nomeadas como "id".
- **Chaves Estrangeiras**: Nomeadas como "[tabela_referenciada]_id" (ex: usuario_id, inspecao_id).

### 3.5.3.1. Dicionário de Dados

#### 3.5.3.2. Visão Geral das Tabelas

| **Nome da Tabela**   | **Descrição**                                                                 |
|----------------------|-------------------------------------------------------------------------------|
| USUARIOS             | Armazena informações dos usuários do sistema.                                 |
| FUNCOES_USUARIOS     | Define as funções/cargos atribuídas a cada usuário.                           |
| INSPECOES            | Armazena informações sobre inspeções específicas.                             |
| EQUIPES_INSPECAO     | Associa usuários a inspeções com funções específicas.                         |
| EDIFICIOS            | Armazena informações sobre os edifícios vinculados a uma inspeção.            |
| PAVIMENTOS           | Armazena informações sobre os pavimentos de um edifício.                      |
| AMBIENTES            | Representa diferentes áreas/cômodos sendo inspecionados em um pavimento.      |
| SISTEMAS             | Representa sistemas construtivos dentro dos ambientes (piso, parede, teto).   |
| PATOLOGIAS           | Armazena manifestações patológicas encontradas nos sistemas.                  |
| FOTOS                | Armazena fotos que documentam as patologias.                                  |
| RELATORIOS           | Armazena os relatórios que foram gerados das inspeções.                       |

### *******. Detalhamento das Tabelas

#### *******.1. USUARIOS

**Descrição**: Armazena informações sobre todos os usuários do sistema.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                     |
|--------------------|------------------|-------------|-----------------|------------------|---------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do usuário.                   |
| nome               | VARCHAR          | 100         | Não             | NULL             | Nome completo do usuário.                         |
| email              | VARCHAR          | 100         | Não             | NULL             | Endereço de email do usuário (deve ser único).    |
| senha              | VARCHAR          | 100         | Não             | NULL             | Senha criptografada do usuário.                   |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.               |

**Chave Primária**: id

**Índices**:
* Um índice UNIQUE pode ser considerado para a coluna `email` para garantir unicidade e otimizar buscas.

#### *******.2. FUNCOES_USUARIOS

**Descrição**: Define as funções atribuídas a cada usuário no sistema (ex: inspetor, coordenador, administrador).

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                     |
|--------------------|------------------|-------------|-----------------|------------------|---------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único da atribuição de função.      |
| usuario_id         | INTEGER          | -           | Não             | NULL             | Referência ao usuário (FK para USUARIOS.id).      |
| funcao             | VARCHAR          | 50          | Não             | NULL             | Função do usuário (ex: inspetor, coordenador).    |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.               |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| usuario_id         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.3. INSPECOES

**Descrição**: Armazena informações sobre inspeções específicas.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                       |
|--------------------|------------------|-------------|-----------------|------------------|-----------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único da inspeção.                    |
| nome_edificio      | VARCHAR          | 200         | Não             | NULL             | Nome do edifício/empreendimento inspecionado.       |
| endereco           | VARCHAR          | 300         | Não             | NULL             | Endereço/localização do edifício.                   |
| tipo_edificio      | VARCHAR          | 50          | Não             | NULL             | Tipo da edificação (residencial, comercial, etc.). |
| torre_bloco        | VARCHAR          | 50          | Não             | NULL             | Torre ou bloco específico, se aplicável.            |
| data_inicio        | DATE             | -           | Não             | NULL             | Data de início da inspeção.                         |
| data_fim           | DATE             | -           | Não             | NULL             | Data de conclusão da inspeção.                      |
| status             | VARCHAR          | 50          | Não             | NULL             | Status atual da inspeção (ex: nao_iniciado).      |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que criou a inspeção (FK).    |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                 |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                  |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.4. EQUIPES_INSPECAO

**Descrição**: Associa usuários a inspeções específicas, definindo quem participa de cada inspeção e com qual função.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único da associação.                     |
| inspecao_id        | INTEGER          | -           | Não             | NULL             | Referência à inspeção (FK para INSPECOES.id).          |
| usuario_id         | INTEGER          | -           | Não             | NULL             | Referência ao usuário (FK para USUARIOS.id).           |
| funcao             | VARCHAR          | 50          | Não             | NULL             | Função do usuário na inspeção (ex: inspetor).          |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| inspecao_id        | INSPECOES               | id                      | (Não especificado)  | (Não especificado)    |
| usuario_id         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.5. EDIFICIOS

**Descrição**: Armazena informações sobre os edifícios vinculados a uma inspeção.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do edifício.                       |
| inspecao_id        | INTEGER          | -           | Não             | NULL             | Referência à inspeção (FK para INSPECOES.id).          |
| numero             | VARCHAR          | 50          | Não             | NULL             | Número ou identificação do edifício.                   |
| Andares            | INTEGER          | -           | Não             | NULL             | Quantidade de andares do edifício.                     |
| Bloco              | VARCHAR          | 50          | Não             | NULL             | Bloco do edifício, se aplicável (ex: Bloco A).         |
| tipo_edificio      | VARCHAR          | 50          | Não             | NULL             | Tipo do edifício (ex: residencial, comercial).         |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que criou o registro (FK).       |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| inspecao_id        | INSPECOES               | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.6. PAVIMENTOS

**Descrição**: Armazena informações sobre os pavimentos de um edifício.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do pavimento.                      |
| edificio_id        | INTEGER          | -           | Não             | NULL             | Referência ao edifício (FK para EDIFICIOS.id).         |
| andar              | VARCHAR          | 50          | Não             | NULL             | Identificação do andar (ex: Térreo, 1º andar).         |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que criou o registro (FK).       |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| edificio_id        | EDIFICIOS               | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.7. AMBIENTES

**Descrição**: Representa diferentes áreas/cômodos sendo inspecionados dentro de um pavimento.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do ambiente.                       |
| pavimento_id       | INTEGER          | -           | Não             | NULL             | Referência ao pavimento (FK para PAVIMENTOS.id).       |
| nome               | VARCHAR          | 100         | Não             | NULL             | Nome do ambiente (ex: sala, cozinha).                  |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que criou o registro (FK).       |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| pavimento_id       | PAVIMENTOS              | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.8. SISTEMAS

**Descrição**: Representa sistemas construtivos dentro dos ambientes (ex: piso, parede, teto).

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do sistema.                        |
| ambiente_id        | INTEGER          | -           | Não             | NULL             | Referência ao ambiente (FK para AMBIENTES.id).         |
| tipo               | VARCHAR          | 100         | Não             | NULL             | Tipo do sistema (ex: piso, parede, teto).              |
| descricao          | TEXT             | -           | Não             | NULL             | Descrição detalhada do sistema.                        |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que criou o registro (FK).       |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| ambiente_id        | AMBIENTES               | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.9. PATOLOGIAS

**Descrição**: Armazena as informações sobre as manifestações patológicas encontradas nos sistemas.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único da patologia.                      |
| sistema_id         | INTEGER          | -           | Não             | NULL             | Referência ao sistema (FK para SISTEMAS.id).           |
| nome               | VARCHAR          | 100         | Não             | NULL             | Nome da manifestação patológica.                       |
| descricao          | TEXT             | -           | Não             | NULL             | Descrição detalhada da patologia.                      |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que registrou a patologia (FK).  |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| sistema_id         | SISTEMAS                | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.10. FOTOS

**Descrição**: Armazena fotos que documentam as patologias encontradas durante a inspeção.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único da foto.                           |
| patologia_id       | INTEGER          | -           | Não             | NULL             | Referência à patologia (FK para PATOLOGIAS.id).        |
| caminho_arquivo    | VARCHAR          | 300         | Não             | NULL             | Caminho para o arquivo da foto.                        |
| legenda            | VARCHAR          | 300         | Não             | NULL             | Legenda descritiva da foto.                            |
| criado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que adicionou a foto (FK).       |
| criado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de criação do registro.                    |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| patologia_id       | PATOLOGIAS              | id                      | (Não especificado)  | (Não especificado)    |
| criado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

#### *******.11. RELATORIOS

**Descrição**: Armazena relatórios técnicos gerados a partir dos dados coletados nas inspeções.

**Colunas**:

| **Nome da Coluna** | **Tipo de Dado** | **Tamanho** | **Obrigatório** | **Valor Padrão** | **Descrição**                                          |
|--------------------|------------------|-------------|-----------------|------------------|--------------------------------------------------------|
| id                 | SERIAL           | -           | Sim             | -                | Identificador único do relatório.                      |
| inspecao_id        | INTEGER          | -           | Não             | NULL             | Referência à inspeção (FK para INSPECOES.id).          |
| titulo             | VARCHAR          | 100         | Não             | NULL             | Título do relatório.                                   |
| status             | VARCHAR          | 100         | Não             | NULL             | Status do relatório (ex: rascunho, final).             |
| gerado_por         | INTEGER          | -           | Não             | NULL             | Referência ao usuário que gerou o relatório (FK).      |
| gerado_em          | TIMESTAMP        | -           | Não             | NULL             | Data e hora de geração do relatório.                   |
| atualizado_em      | TIMESTAMP        | -           | Não             | NULL             | Data e hora da última atualização.                     |

**Chave Primária**: id

**Chaves Estrangeiras**:

| **Nome da Coluna** | **Tabela Referenciada** | **Coluna Referenciada** | **Ação ao Excluir** | **Ação ao Atualizar** |
|--------------------|-------------------------|-------------------------|---------------------|-----------------------|
| inspecao_id        | INSPECOES               | id                      | (Não especificado)  | (Não especificado)    |
| gerado_por         | USUARIOS                | id                      | (Não especificado)  | (Não especificado)    |

### *******. Relacionamentos

#### *******. Visão Geral dos Relacionamentos

| **Tabela de Origem** | **Tabela de Destino** | **Tipo de Relacionamento** | **Descrição**                                                                 |
|----------------------|-----------------------|----------------------------|-------------------------------------------------------------------------------|
| USUARIOS             | FUNCOES_USUARIOS      | 1:N                        | Um usuário pode ter múltiplas atribuições de funções (histórico ou diferentes contextos). |
| USUARIOS             | EQUIPES_INSPECAO      | 1:N                        | Um usuário pode participar de múltiplas equipes de inspeção.                  |
| USUARIOS             | INSPECOES             | 1:N                        | Um usuário (criador) pode criar múltiplas inspeções.                          |
| USUARIOS             | EDIFICIOS             | 1:N                        | Um usuário (criador) pode registrar múltiplos edifícios.                      |
| USUARIOS             | PAVIMENTOS            | 1:N                        | Um usuário (criador) pode registrar múltiplos pavimentos.                     |
| USUARIOS             | AMBIENTES             | 1:N                        | Um usuário (criador) pode registrar múltiplos ambientes.                      |
| USUARIOS             | SISTEMAS              | 1:N                        | Um usuário (criador) pode registrar múltiplos sistemas.                       |
| USUARIOS             | PATOLOGIAS            | 1:N                        | Um usuário (criador) pode registrar múltiplas patologias.                     |
| USUARIOS             | FOTOS                 | 1:N                        | Um usuário (criador) pode adicionar múltiplas fotos.                          |
| USUARIOS             | RELATORIOS            | 1:N                        | Um usuário (gerador) pode gerar múltiplos relatórios.                         |
| INSPECOES            | EQUIPES_INSPECAO      | 1:N                        | Uma inspeção pode ter múltiplos usuários em sua equipe.                       |
| INSPECOES            | EDIFICIOS             | 1:N                        | Uma inspeção pode estar associada a múltiplos edifícios (se aplicável ao modelo de negócio, geralmente é 1:1 ou 1:N para edifícios dentro de um complexo maior da inspeção). O script `init.sql` sugere que uma inspeção pode ter vários edifícios. |
| INSPECOES            | RELATORIOS            | 1:1 ou 1:N                 | Uma inspeção geralmente resulta em um relatório, mas pode haver versões. O script `init.sql` permite múltiplos relatórios por inspeção. |
| EDIFICIOS            | PAVIMENTOS            | 1:N                        | Um edifício pode ter múltiplos pavimentos.                                    |
| PAVIMENTOS           | AMBIENTES             | 1:N                        | Um pavimento pode ter múltiplos ambientes.                                    |
| AMBIENTES            | SISTEMAS              | 1:N                        | Um ambiente pode ter múltiplos sistemas construtivos.                         |
| SISTEMAS             | PATOLOGIAS            | 1:N                        | Um sistema pode apresentar múltiplas patologias.                              |
| PATOLOGIAS           | FOTOS                 | 1:N                        | Uma patologia pode ser documentada com múltiplas fotos.                       |

### *******. Restrições de Integridade e Regras de Validação

| **Tabela de Origem** | **Tabela de Destino** | **Tipo de Relacionamento** | **Descrição**                                                                 |
|----------------------|-----------------------|----------------------------|-------------------------------------------------------------------------------|
| USUARIOS             | FUNCOES_USUARIOS      | 1:N                        | Um usuário pode ter múltiplas atribuições de funções (histórico ou diferentes contextos). |
| USUARIOS             | EQUIPES_INSPECAO      | 1:N                        | Um usuário pode participar de múltiplas equipes de inspeção.                  |
| USUARIOS             | INSPECOES             | 1:N                        | Um usuário (criador) pode criar múltiplas inspeções.                          |
| USUARIOS             | EDIFICIOS             | 1:N                        | Um usuário (criador) pode registrar múltiplos edifícios.                      |
| USUARIOS             | PAVIMENTOS            | 1:N                        | Um usuário (criador) pode registrar múltiplos pavimentos.                     |
| USUARIOS             | AMBIENTES             | 1:N                        | Um usuário (criador) pode registrar múltiplos ambientes.                      |
| USUARIOS             | SISTEMAS              | 1:N                        | Um usuário (criador) pode registrar múltiplos sistemas.                       |
| USUARIOS             | PATOLOGIAS            | 1:N                        | Um usuário (criador) pode registrar múltiplas patologias.                     |
| USUARIOS             | FOTOS                 | 1:N                        | Um usuário (criador) pode adicionar múltiplas fotos.                          |
| USUARIOS             | RELATORIOS            | 1:N                        | Um usuário (gerador) pode gerar múltiplos relatórios.                         |
| INSPECOES            | EQUIPES_INSPECAO      | 1:N                        | Uma inspeção pode ter múltiplos usuários em sua equipe.                       |
| INSPECOES            | EDIFICIOS             | 1:N                        | Uma inspeção pode estar associada a múltiplos edifícios (se aplicável ao modelo de negócio, geralmente é 1:1 ou 1:N para edifícios dentro de um complexo maior da inspeção). O script `init.sql` sugere que uma inspeção pode ter vários edifícios. |
| INSPECOES            | RELATORIOS            | 1:1 ou 1:N                 | Uma inspeção geralmente resulta em um relatório, mas pode haver versões. O script `init.sql` permite múltiplos relatórios por inspeção. |
| EDIFICIOS            | PAVIMENTOS            | 1:N                        | Um edifício pode ter múltiplos pavimentos.                                    |
| PAVIMENTOS           | AMBIENTES             | 1:N                        | Um pavimento pode ter múltiplos ambientes.                                    |
| AMBIENTES            | SISTEMAS              | 1:N                        | Um ambiente pode ter múltiplos sistemas construtivos.                         |
| SISTEMAS             | PATOLOGIAS            | 1:N                        | Um sistema pode apresentar múltiplas patologias.                              |
| PATOLOGIAS           | FOTOS                 | 1:N                        | Uma patologia pode ser documentada com múltiplas fotos.                       |

### *******. Restrições de Domínio

| **Tabela**       | **Coluna**         | **Restrição**                                      | **Descrição**                                                                    |
|------------------|--------------------|----------------------------------------------------|----------------------------------------------------------------------------------|
| USUARIOS         | email              | UNIQUE                                             | Garante que cada email seja único no sistema.                                    |
| INSPECOES        | status             | CHECK (status IN (...valores permitidos...))       | Garante que o status da inspeção seja um valor válido (ex: 'nao_iniciado', 'em_andamento', 'concluido')). |
| FUNCOES_USUARIOS | funcao             | CHECK (funcao IN (...valores permitidos...))       | Garante que a função do usuário seja válida (ex: 'inspetor', 'coordenador')).   |
| EQUIPES_INSPECAO | funcao             | CHECK (funcao IN (...valores permitidos...))       | Garante que a função na equipe seja válida.                                      |
| EDIFICIOS        | tipo_edificio      | CHECK (tipo_edificio IN (...valores...))           | Garante que o tipo de edifício seja válido.                                      |
| SISTEMAS         | tipo               | CHECK (tipo IN ('piso', 'parede', 'teto', ...))    | Garante que o tipo de sistema seja um valor conhecido.                           |
| RELATORIOS       | status             | CHECK (status IN ('rascunho', 'final', ...))       | Garante que o status do relatório seja válido.                                   |

### *******. Restrições de Entidade

As restrições de entidade garantem a unicidade de cada registro em uma tabela, geralmente através de chaves primárias e restrições UNIQUE.

| Tabela             | Restrição                                  | Descrição                                                                              |
|--------------------|--------------------------------------------|----------------------------------------------------------------------------------------|
| USUARIOS           | PRIMARY KEY (id)                           | Identificador único para cada usuário.                                                 |
| USUARIOS           | UNIQUE (email)                             | Garante que cada endereço de email seja único no sistema. |
| INSPECOES          | PRIMARY KEY (id)                           | Identificador único para cada inspeção.                                                |
| RELATORIOS         | PRIMARY KEY (id)                           | Identificador único para cada relatório.                                               |
| FUNCOES_USUARIOS   | PRIMARY KEY (id)                           | Identificador único para cada atribuição de função.                                    |
| FUNCOES_USUARIOS   | UNIQUE (usuario_id, funcao)                | Garante que um usuário não tenha a mesma função atribuída múltiplas vezes. |
| EQUIPES_INSPECAO   | PRIMARY KEY (id)                           | Identificador único para cada membro da equipe de inspeção.                            |
| EQUIPES_INSPECAO   | UNIQUE (inspecao_id, usuario_id, funcao)   | Garante que um usuário não tenha a mesma função na mesma inspeção.      |
| EDIFICIOS          | PRIMARY KEY (id)                           | Identificador único para cada edifício.                                                |
| PAVIMENTOS         | PRIMARY KEY (id)                           | Identificador único para cada pavimento.                                               |
| AMBIENTES          | PRIMARY KEY (id)                           | Identificador único para cada ambiente.                                                |
| SISTEMAS           | PRIMARY KEY (id)                           | Identificador único para cada sistema.                                                 |
| PATOLOGIAS         | PRIMARY KEY (id)                           | Identificador único para cada patologia.                                               |
| FOTOS              | PRIMARY KEY (id)                           | Identificador único para cada foto.                                                    |

### 3.5.5.4. Restrições de Referência

As restrições de referência, ou chaves estrangeiras (FOREIGN KEY), garantem a integridade dos dados entre tabelas relacionadas.

| Tabela de Origem   | Coluna de Origem | Tabela de Destino | Coluna de Destino | Ação ao Excluir   | Ação ao Atualizar | Descrição                                                              |
|--------------------|------------------|-------------------|-------------------|-------------------|-------------------|------------------------------------------------------------------------|
| FUNCOES_USUARIOS   | usuario_id       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que a função seja atribuída a um usuário existente.              |
| INSPECOES          | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador da inspeção seja um usuário existente.           |
| RELATORIOS         | inspecao_id      | INSPECOES         | id                | (Não especificado)| (Não especificado)| Garante que o relatório pertença a uma inspeção existente.              |
| RELATORIOS         | gerado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o gerador do relatório seja um usuário existente.          |
| EQUIPES_INSPECAO   | inspecao_id      | INSPECOES         | id                | (Não especificado)| (Não especificado)| Garante que o membro da equipe pertença a uma inspeção existente.       |
| EQUIPES_INSPECAO   | usuario_id       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o membro da equipe seja um usuário existente.                |
| EDIFICIOS          | inspecao_id      | INSPECOES         | id                | (Não especificado)| (Não especificado)| Garante que o edifício pertença a uma inspeção existente.               |
| EDIFICIOS          | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador do edifício seja um usuário existente.           |
| PAVIMENTOS         | edificio_id      | EDIFICIOS         | id                | (Não especificado)| (Não especificado)| Garante que o pavimento pertença a um edifício existente.               |
| PAVIMENTOS         | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador do pavimento seja um usuário existente.          |
| AMBIENTES          | pavimento_id     | PAVIMENTOS        | id                | (Não especificado)| (Não especificado)| Garante que o ambiente pertença a um pavimento existente.               |
| AMBIENTES          | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador do ambiente seja um usuário existente.           |
| SISTEMAS           | ambiente_id      | AMBIENTES         | id                | (Não especificado)| (Não especificado)| Garante que o sistema pertença a um ambiente existente.                 |
| SISTEMAS           | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador do sistema seja um usuário existente.            |
| PATOLOGIAS         | sistema_id       | SISTEMAS          | id                | (Não especificado)| (Não especificado)| Garante que a patologia pertença a um sistema existente.                |
| PATOLOGIAS         | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador da patologia seja um usuário existente.          |
| FOTOS              | patologia_id     | PATOLOGIAS        | id                | (Não especificado)| (Não especificado)| Garante que a foto pertença a uma patologia existente.                  |
| FOTOS              | criado_por       | USUARIOS          | id                | (Não especificado)| (Não especificado)| Garante que o criador da foto seja um usuário existente.               |

### 3.5.5.5. Regras de Validação Personalizadas

Regras de validação personalizadas podem ser implementadas via CHECK constraints ou triggers para impor lógica de negócios específica.

| Tabela             | Coluna(s) Afetada(s)                               | Regra Potencial                            | Descrição                                                                    | Implementação Comum |
|--------------------|--------------------------|--------------------------------------------|------------------------------------------------------------------------------|---------------------|
| INSPECOES          | status                   | CHECK (status IN (...))                    | Garante que o status da inspeção seja um dos valores predefinidos.           | CHECK constraint    |
| INSPECOES          | data_inicio, data_fim    | CHECK (data_fim >= data_inicio)            | Garante que a data de fim não seja anterior à data de início, se ambas preenchidas. | CHECK constraint    |
| RELATORIOS         | status                   | CHECK (status IN (...))                    | Garante que o status do relatório seja um dos valores predefinidos.          | CHECK constraint    |
| FUNCOES_USUARIOS   | funcao                   | CHECK (funcao IN (...))                    | Garante que a função do usuário seja um dos valores predefinidos.            | CHECK constraint    |
| EQUIPES_INSPECAO   | funcao                   | CHECK (funcao IN (...))                    | Garante que a função do membro da equipe seja um dos valores predefinidos.   | CHECK constraint    |
| EDIFICIOS          | tipo_edificio            | CHECK (tipo_edificio IN (...))             | Garante que o tipo de edifício seja válido.                                  |
| SISTEMAS           | tipo                     | CHECK (tipo IN ('piso', 'parede', 'teto', ...))    | Garante que o tipo de sistema seja um dos valores predefinidos.              | CHECK constraint    |
| USUARIOS           | email                    | Validação de formato de email              | Garante que o email siga um formato válido.                                  | CHECK / Aplicação   |

## *******. Tipos Personalizados

Embora o SQL padrão não tenha "tipos personalizados" da mesma forma que algumas linguagens de programação, domínios ou ENUMs (em SGBDs que os suportam) podem ser usados para restringir os valores de certas colunas.

| "Tipo" Lógico         | Coluna(s) Afetada(s)                               | Valores Sugeridos         | Descrição                                                              |
|-----------------------|----------------------------------------------------|--------------------------------------------------------|------------------------------------------------------------------------|
| status_inspecao       | INSPECOES.status                                   | 'nao_iniciado', 'em_andamento', 'concluido'            | Define os status possíveis para inspeções.                             |
| status_relatorio      | RELATORIOS.status                                  | 'rascunho', 'final'                                    | Define os status possíveis para relatórios.                            |
| funcao_usuario_sistema| FUNCOES_USUARIOS.funcao, EQUIPES_INSPECAO.funcao   | 'inspetor', 'coordenador', 'administrador'             | Define as funções possíveis para usuários no sistema ou em inspeções.  |
| tipo_edificacao       | EDIFICIOS.tipo_edificio, INSPECOES.tipo_edificio   | 'residencial', 'comercial', 'misto'                    | Define os tipos de edificação.                                         |
| tipo_sistema_constr   | SISTEMAS.tipo                                      | 'piso', 'parede', 'teto'                               | Define os tipos de sistemas construtivos.                              |

## *******. Apêndices

### *******. Scripts SQL

Scripts SQL são conjuntos de comandos escritos em Structured Query Language (SQL) usados para interagir com bancos de dados relacionais. Eles permitem executar tarefas como criação de tabelas, inserção e atualização de dados, consultas e exclusões de registros de forma automatizada. Esses scripts são fundamentais para garantir a consistência da estrutura e dos dados do banco ao longo do desenvolvimento, sendo amplamente utilizados em processos de migração, população inicial de dados e gerenciamento de esquemas.

No nosso desenvolvimento web, foi criado um script SQL para cada tabela do banco de dados, com base no modelo conceitual previamente definido. Essa abordagem foi adotada para garantir que a estrutura do banco fosse criada de forma padronizada, organizada e alinhada aos requisitos do sistema. Além disso, o uso de scripts facilita a replicação do banco em diferentes ambientes, como desenvolvimento, testes e produção.

<img src="../assets/tabela1.png">
<img src="../assets/tabela2.png">
<img src="../assets/tabela3.png">
<img src="../assets/tabela4.png">
<img src="../assets/tabela5.png">
<img src="../assets/tabelas6.png">
<img src="../assets/tabelas7.png">
<img src="../assets/tabela8.png">
<img src="../assets/tabela9.png">
<img src="../assets/tabela10.png">
<img src="../assets/tabela11.png">
<img src="../assets/tabela12.png">

Cada uma dessas tabelas foi criada baseada na modelagem conceitual, fazendo as devidas conversões para a linguagem SQL. É importante ressaltar que o código respeita a chaves estrangeiras, indicando exatamente quais são suas relações. Além disso, a linguagem sql converte os comandos e os deixa legível para o código.

# Principais Conversões entre Modelo Conceitual e Dados SQL 


| Tipo Conceitual         | Tipo SQL (PostgreSQL / padrão ANSI) | Observações                                          |
|-------------------------|--------------------------------------|------------------------------------------------------|
| Identificador (ID)      | `SERIAL` ou `INT AUTO_INCREMENT`     | Gera valor sequencial automaticamente                |
| Número inteiro          | `INT`, `INTEGER`                     | Pode usar `SMALLINT`, `BIGINT` para variações        |
| Texto (curto)           | `VARCHAR(n)`                         | Ex: `VARCHAR(100)`                                   |
| Texto (longo)           | `TEXT`                               | Armazena grandes volumes de texto                    |
| Data                    | `DATE`                               | Apenas data (ano, mês, dia)                          |
| Hora                    | `TIME`                               | Apenas hora (hora:minuto:segundo)                    |
| Data e hora             | `TIMESTAMP`                          | Data + hora (pode incluir fuso horário)              |
| Booleano (V ou F)       | `BOOLEAN`                            | Aceita `TRUE`, `FALSE`, `1`, `0`                     |

### 3.5.8.3. Exemplos de Dados

A utilização de dados de teste é uma prática essencial no processo de desenvolvimento de sistemas, especialmente durante as fases de construção, validação e homologação. Eles servem como uma simulação controlada do comportamento do sistema diante de diferentes cenários, permitindo que desenvolvedores, testadores e analistas verifiquem se as funcionalidades estão operando corretamente, de acordo com os requisitos estabelecidos.

Ao trabalhar com dados de teste, é possível detectar erros lógicos, inconsistências em regras de negócio, falhas de segurança e problemas de desempenho de forma antecipada, antes que o sistema entre em produção. Isso reduz significativamente os riscos de falhas críticas no ambiente real e aumenta a confiabilidade do software.

Além disso, dados de teste contribuem para a criação de um ambiente de desenvolvimento mais seguro, pois evitam o uso de dados sensíveis ou reais que poderiam comprometer a privacidade de usuários. Eles também são fundamentais para garantir que testes automatizados sejam reproduzíveis e confiáveis ao longo do tempo.

Baseado nisso, foram criados dados fictícios para validação do SQL:

<img src="../assets/dados1.png">
<img src="../assets/dados2.png">
<img src="../assets/dados3.png">

### *******. Glossário

- **Inspeção Predial**: Avaliação técnica das condições de uma edificação
- **Manifestação Patológica**: Problema ou anomalia identificada em um sistema construtivo
- **Sistema Construtivo**: Componente da edificação como piso, parede ou teto
- **Ambiente**: Área ou cômodo específico dentro de uma edificação
- **Relatório Técnico**: Documento que resume os achados de uma inspeção predial

### 3.5.9. Consultas SQL e lógica proposicional (sprint 2)


As consultas SQL são comandos utilizados para buscar, filtrar e manipular dados no banco de dados, permitindo que usuários e sistemas acessem informações relevantes de forma estruturada e eficiente. Já a lógica proposicional é uma ferramenta matemática que permite representar e analisar as condições lógicas envolvidas nessas consultas.

Ao associar cada condição de uma consulta SQL a uma proposição lógica, é possível traduzir a lógica da consulta para uma expressão formal, tornando mais claro o funcionamento dos filtros aplicados. No projeto, essa abordagem contribui para a clareza, rastreabilidade e validação das operações realizadas sobre os dados.

---

#1 | ---
--- | ---
**Expressão SQL** | SELECT * FROM projetos WHERE (status = 'em_andamento' OR criado_por = 1);
**Proposições lógicas** | $A$: O status do projeto é 'em_andamento' (status = 'em_andamento')<br>$B$: O projeto foi criado pelo usuário 1 (criado_por = 1)
**Expressão lógica proposicional** | $A \lor B$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$A \lor B$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>V</td></tr><tr><td>V</td><td>V</td><td>V</td></tr></tbody></table>

- **Consulta 1:** Seleciona todos os projetos que estão em andamento ou que foram criados pelo usuário de ID 1. Essa consulta permite visualizar tanto os projetos ativos quanto aqueles sob responsabilidade de um usuário específico.

---

#2 | ---
--- | ---
**Expressão SQL** | SELECT * FROM ambientes WHERE (nome = 'Sala' AND criado_por <> 2);
**Proposições lógicas** | $A$: O nome do ambiente é 'Sala' (nome = 'Sala')<br>$B$: O ambiente não foi criado pelo usuário 2 (criado_por ≠ 2)
**Expressão lógica proposicional** | $A \land B$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$A \land B$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td></tr><tr><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>V</td><td>V</td></tr></tbody></table>

- **Consulta 2:** Busca todos os ambientes cujo nome seja "Sala" e que não tenham sido criados pelo usuário de ID 2. Assim, é possível filtrar ambientes por tipo e por responsável, excluindo registros de um determinado usuário.

---

#3 | ---
--- | ---
**Expressão SQL** | SELECT * FROM patologias WHERE (nome = 'Fissura' OR descricao IS NOT NULL) AND sistema_id = 5;
**Proposições lógicas** | $A$: O nome da patologia é 'Fissura' (nome = 'Fissura')<br>$B$: A descrição não está vazia (descricao IS NOT NULL)<br>$C$: O sistema_id é 5 (sistema_id = 5)
**Expressão lógica proposicional** | $(A \lor B) \land C$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$(A \lor B)$</th><th>$(A \lor B) \land C$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>V</td><td>F</td></tr><tr><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td></tr></tbody></table>

Consulta 3: Retorna todas as manifestações patológicas cujo nome seja "Fissura" ou que tenham descrição preenchida, desde que estejam associadas ao sistema de ID 5. A expressão lógica foi corrigida para refletir corretamente o uso de NOT.

---

#4 | ---
--- | ---
Expressão SQL | UPDATE patologias SET gravidade = 'alta' WHERE observacao ILIKE '%risco estrutural%' AND gravidade != 'alta';
Proposições lógicas | $A$: A observação menciona "risco estrutural" (observacao ILIKE '%risco estrutural%')<br>$B$: A gravidade é "alta" (gravidade = 'alta')
Expressão lógica proposicional | $A \land \lnot B$
Tabela Verdade | <table><thead><tr><th>$A$</th><th>$B$</th><th>$\lnot B$</th><th>$A \land \lnot B$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>F</td></tr></tbody></table>

Consulta 4: Atualiza automaticamente o nível de gravidade de uma patologia para “alta” caso a observação textual indique a expressão “risco estrutural” e ainda não esteja marcada como tal. Essa ação melhora a consistência dos dados técnicos, garante atenção a problemas críticos e facilita a geração de relatórios mais confiáveis e automatizados.

---

#5 | ---
--- | ---
Expressão SQL | DELETE FROM anotacoes WHERE sistema_id IN (10, 11, 12) OR texto IS NULL;
Proposições lógicas | $A$: O sistema está entre os IDs 10, 11 ou 12 (sistema_id IN (10, 11, 12))<br>$B$: O texto está preenchido (texto IS NOT NULL)
Expressão lógica proposicional | $A \lor \lnot B$
Tabela Verdade | <table><thead><tr><th>$A$</th><th>$B$</th><th>$\lnot B$</th><th>$A \lor \lnot B$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>V</td><td>V</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>V</td></tr></tbody></table>

Consulta 5: Remove todas as anotações associadas a sistemas considerados obsoletos (IDs 10, 11, 12) ou que não possuem nenhum texto preenchido.

---

#6 | ---
--- | ---
Expressão SQL | SELECT * FROM ambientes WHERE (nome = 'Cobertura' OR nome = 'Terraço') AND NOT criado_por = 5;
Proposições lógicas | $A$: O nome é 'Cobertura' (nome = 'Cobertura')<br>$B$: O nome é 'Terraço' (nome = 'Terraço')<br>$C$: O ambiente foi criado pelo usuário 5 (criado_por = 5)
Expressão lógica proposicional | $(A \lor B) \land \lnot C$
Tabela Verdade | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$A \lor B$</th><th>$\lnot C$</th><th>$(A \lor B) \land \lnot C$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td><td>V</td><td>F</td><td>F</td></tr></tbody></table>


Consulta 6: Seleciona todos os ambientes cujo nome seja "Cobertura" ou "Terraço", excluindo os criados pelo usuário 5. É útil para focar em áreas críticas, ignorando entradas específicas.

## 3.6. WebAPI e endpoints (sprints 3 e 4)

Esta seção descreve a Web API desenvolvida para a aplicação, detalhando os endpoints disponíveis, seus respectivos métodos HTTP e os formatos esperados de requisição e resposta.

A Web API serve como a camada de comunicação entre o frontend e o backend, permitindo que as funcionalidades do sistema sejam acessadas por meio de requisições HTTP. Com isso, é possível realizar operações como criação, consulta, atualização e exclusão de dados de forma estruturada, segura e padronizada.

Cada endpoint representa uma rota da aplicação responsável por executar uma ação específica, como cadastrar usuários, listar tarefas, ou atualizar eventos. Esta documentação tem como objetivo fornecer uma visão clara de como interagir com a API, facilitando a integração e o desenvolvimento contínuo do sistema.

Rota |  Método  |  Header  |  Body  | Formatos de response 
:---:|:---:|:---:|:---:|:-------:
/usuarios | POST | application.json | nome, email, senha, criado_em | JSON |
/relatorios | POST | application.json | inspecao_id, titulo, resumo, status, gerado_por | JSON |
/inspecoes | POST | application.json | projeto_id, titulo, nome_edificio, endereco, tipo_edificio, torre_bloco, data_inicio, data_fim, status, criado_por | JSON |
/funcoesUsuario | POST | application.json | usuario_id, funcao | JSON |
/equipesInspecao | POST | application.json | inspecao_id, usuario_id, funcao | JSON |
/edificio | POST | application.json | inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em | JSON |
/pavimento | POST | application.json | edificio_id, andar, criado_por, criado_em, atualizado_em | JSON |
/ambiente | POST | application.json | pavimento_id, nome, criado_por, criado_em, atualizado_em | JSON |
/sistemas | POST | application.json | ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em | JSON |
/patologias | POST | application.json | sistema_id, nome, descricao, criado_por, criado_em, atualizado_em | JSON |
/fotos | POST | application.json | patologia_id, caminho_arquivo, legenda, criado_por, criado_em | JSON |

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. Primeira versão da aplicação web (sprint 3)

Durante esta Sprint, foi realizado o desenvolvimento do front-end da aplicação utilizando as tecnologias HTML (HyperText Markup Language) e CSS (Cascading Style Sheets). Paralelamente, avançou-se também na integração entre o front-end e o back-end, por meio da criação de controllers, models e rotas. Essa estrutura tem como objetivo permitir que o banco de dados se comunique adequadamente com os componentes da aplicação, possibilitando, futuramente, o armazenamento e a manipulação das informações inseridas por meio da interface do sistema.

### Front-End

O front-end corresponde à parte visual de um site ou sistema com a qual o usuário interage diretamente. Inclui elementos como textos, botões e formulários, sendo construído com HTML para definir a estrutura, CSS para o estilo visual e JavaScript para adicionar interatividade. Trata-se da camada visível da aplicação, em contraste com o back-end, que opera nos bastidores, processando os dados e regras de negócio.

Até o momento, foram desenvolvidas 18 telas da aplicação. A seguir, são apresentadas algumas delas como exemplo.

Abaixo, encontra-se a tela de login, a qual é a primeira visualizada pelo usuário ao acessar a aplicação:

<div align='center'> 
<img src='../assets/telaLogin.png'> 
</div>

Após o login, o sistema direciona o usuário para diferentes interfaces, conforme seu cargo:

Caso o usuário seja um inspetor:

<div align='center'> 
<img src='../assets/telaInspecoes.png'> 
</div>

Caso o usuário possua perfil de administrador:

<div align='center'> 
<img src='../assets/telaAdm.png'> 
</div>

Essas são algumas das telas desenvolvidas no período de duas semanas. Outras interfaces também foram implementadas, mas não serão apresentadas nesta seção.

### Back-End

O back-end é a camada da aplicação responsável pelo processamento dos dados, execução das regras de negócio e comunicação com o banco de dados. Embora essa parte não seja visível ao usuário, ela é essencial para o funcionamento do sistema. As tecnologias utilizadas no desenvolvimento do back-end incluem a linguagem JavaScript, com o uso do ambiente Node.js, e o banco de dados PostgreSQL. Em resumo, o back-end garante que todas as funcionalidades ocorram corretamente por trás da interface apresentada ao usuário.

Durante a Sprint anterior e esta Sprint, foram realizadas a modelagem do banco de dados, melhorias estruturais e a implementação da conexão com o sistema. O código referente à criação do banco de dados está apresentado abaixo:

```sql

CREATE TABLE IF NOT EXISTS usuarios (
  id SERIAL PRIMARY KEY,
  nome VARCHAR(100),
  email VARCHAR(100),
  senha VARCHAR(100),
  criado_em TIMESTAMP
);

CREATE TABLE IF NOT EXISTS inspecoes (
  id SERIAL PRIMARY KEY,
  nome_edificio VARCHAR(200),
  endereco VARCHAR(300),
  tipo_edificio VARCHAR(50),
  torre_bloco VARCHAR(50),
  data_inicio DATE,
  data_fim DATE,
  status VARCHAR(50), -- nao_iniciado, em_andamento, concluido
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS relatorios (
  id SERIAL PRIMARY KEY,
  inspecao_id INTEGER,
  titulo VARCHAR(100),
  status VARCHAR(100), -- rascunho, final
  gerado_por INTEGER,
  gerado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
  FOREIGN KEY (gerado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS funcoes_usuarios (
  id SERIAL PRIMARY KEY,
  usuario_id INTEGER,
  funcao VARCHAR(50), -- inspetor, coordenador, administrador
  criado_em TIMESTAMP,
  FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS equipes_inspecao (
  id SERIAL PRIMARY KEY,
  inspecao_id INTEGER,
  usuario_id INTEGER,
  funcao VARCHAR(50), -- inspetor, coordenador, administrador
  criado_em TIMESTAMP,
  FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
  FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);
 
 --deb
CREATE TABLE IF NOT EXISTS edificios(
    id SERIAL PRIMARY KEY,
    inspecao_id INTEGER,
    numero VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
    Andares INTEGER,
    Bloco VARCHAR(50), -- Bloco A, Bloco B, etc
    tipo_edificio VARCHAR(50), -- residencial, comercial, misto
    criado_por INTEGER,
    criado_em TIMESTAMP,
    atualizado_em TIMESTAMP,
    FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
    FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS pavimentos(
    id SERIAL PRIMARY KEY,
    edificio_id INTEGER,
    andar VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
    criado_por INTEGER,
    criado_em TIMESTAMP,
    atualizado_em TIMESTAMP,
    FOREIGN KEY (edificio_id) REFERENCES edificios(id),
    FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS ambientes (
  id SERIAL PRIMARY KEY,
  pavimento_id INTEGER,
  nome VARCHAR(100),
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (pavimento_id) REFERENCES pavimentos(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS sistemas (
  id SERIAL PRIMARY KEY,
  ambiente_id INTEGER,
  tipo VARCHAR(100), -- piso, parede, teto
  descricao TEXT,
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (ambiente_id) REFERENCES ambientes(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS patologias (
  id SERIAL PRIMARY KEY,
  sistema_id INTEGER,
  nome VARCHAR(100),
  descricao TEXT,
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (sistema_id) REFERENCES sistemas(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS fotos (
  id SERIAL PRIMARY KEY,
  patologia_id INTEGER,
  caminho_arquivo VARCHAR(300),
  legenda VARCHAR(300),
  criado_por INTEGER,
  criado_em TIMESTAMP,
  FOREIGN KEY (patologia_id) REFERENCES patologias(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

```

Após a modelagem do banco, foi necessário estabelecer sua conexão com o restante da aplicação, o que representou um dos principais focos desta etapa. Para isso, foi adotado o padrão arquitetural MVC.

O padrão MVC (Model-View-Controller) é amplamente utilizado no desenvolvimento de aplicações web, pois organiza o código em três camadas distintas: o Model (Modelo), a View (Visão) e o Controller (Controlador). Essa separação facilita a manutenção, evolução e organização do sistema.

Model (Modelo): responsável por manipular os dados da aplicação e interagir diretamente com o banco de dados.

View (Visão): representa a interface visual do sistema, exibindo informações ao usuário e recebendo suas interações.

Controller (Controlador): atua como intermediário entre a View e o Model, processando requisições do usuário, chamando o Model quando necessário e retornando respostas apropriadas à View.

A seguir, são apresentados exemplos de controllers, models e rotas da aplicação. As views serão abordadas na próxima seção.

```javaScript
// controllers/userController.js
const userService = require('../services/userService');

const getAllUsers = async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.status(200).json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getUserById = async (req, res) => {
  try {
    const user = await userService.getUserById(req.params.id);
    if (user) {
      res.status(200).json(user);
    } else {
      res.status(404).json({ error: 'Usuário não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createUser = async (req, res) => {
  try {
    const { name, email } = req.body;
    const newUser = await userService.createUser(name, email);
    res.status(201).json(newUser);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateUser = async (req, res) => {
  try {
    const { name, email } = req.body;
    const updatedUser = await userService.updateUser(req.params.id, name, email);
    if (updatedUser) {
      res.status(200).json(updatedUser);
    } else {
      res.status(404).json({ error: 'Usuário não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteUser = async (req, res) => {
  try {
    const deletedUser = await userService.deleteUser(req.params.id);
    if (deletedUser) {
      res.status(200).json(deletedUser);
    } else {
      res.status(404).json({ error: 'Usuário não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};

```

```javaScript
//models/userModel.js
    const db = require('../config/db');

class User {
  static async getAll() {
    const result = await db.query('SELECT * FROM users');
    return result.rows;
  }

  static async getById(id) {
    const result = await db.query('SELECT * FROM users WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(data) {
    const result = await db.query(
      'INSERT INTO users (name, email) VALUES ($1, $2) RETURNING *',
      [data.name, data.email]
    );
    return result.rows[0];
  }

  static async update(id, data) {
    const result = await db.query(
      'UPDATE users SET name = $1, email = $2 WHERE id = $3 RETURNING *',
      [data.name, data.email, id]
    );
    return result.rows[0];
  }

  static async delete(id) {
    const result = await db.query('DELETE FROM users WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = User;

```

```javaScript
//routes/userRoutes
const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');

router.get('/', userController.getAllUsers);
router.get('/:id', userController.getUserById);
router.post('/', userController.createUser);
router.put('/:id', userController.updateUser);
router.delete('/:id', userController.deleteUser);

module.exports = router;

```

Em resumo, nesta Sprint foram estruturadas as interfaces do sistema e estabelecida a comunicação entre o front-end e o back-end, criando uma base sólida para a integração com o banco de dados e o desenvolvimento das próximas fases do projeto.

## 4.2. Segunda versão da aplicação web (sprint 4)

*Descreva e ilustre aqui o desenvolvimento da sua segunda versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

## 4.3. Versão final da aplicação web (sprint 5)

*Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados (sprint 4)

*Liste e descreva os testes unitários dos endpoints criados, automatizados e planejados para sua solução. Posicione aqui também o relatório de cobertura de testes Jest se houver (através de link ou transcrito para estrutura markdown)*

## 5.2. Testes de usabilidade (sprint 5)

*Posicione aqui as tabelas com enunciados de tarefas, etapas e resultados de testes de usabilidade. Ou utilize um link para seu relatório de testes (mantenha o link sempre público para visualização)*

# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing (sprint 4)

## 6.1 Resumo Executivo

*Preencher com até 300 palavras, sem necessidade de fonte*

*Apresente de forma clara e objetiva os principais destaques do projeto: oportunidades de mercado, diferenciais competitivos da aplicação web e os objetivos estratégicos pretendidos.*

## 6.2 Análise de Mercado

*a) Visão Geral do Setor (até 250 palavras)*
*Contextualize o setor no qual a aplicação está inserida, considerando aspectos econômicos, tecnológicos e regulatórios. Utilize fontes confiáveis.*

*b) Tamanho e Crescimento do Mercado (até 250 palavras)*
*Apresente dados quantitativos sobre o tamanho atual e projeções de crescimento do mercado. Utilize fontes confiáveis.*

*c) Tendências de Mercado (até 300 palavras)*
*Identifique e analise tendências relevantes (tecnológicas, comportamentais e mercadológicas) que influenciam o setor. Utilize fontes confiáveis.*

## 6.3 Análise da Concorrência

*a) Principais Concorrentes (até 250 palavras)*
*Liste os concorrentes diretos e indiretos, destacando suas principais características e posicionamento no mercado.*

*b) Vantagens Competitivas da Aplicação Web (até 250 palavras)*
*Descreva os diferenciais da sua aplicação em relação aos concorrentes, sem necessidade de citação de fontes.*


## 6.4 Público-Alvo

*a) Segmentação de Mercado (até 250 palavras)*
Descreva os principais segmentos de mercado a serem atendidos pela aplicação. Utilize bases de dados e fontes confiáveis.*

*b) Perfil do Público-Alvo (até 250 palavras)*
*Caracterize o público-alvo com dados demográficos, psicográficos e comportamentais, incluindo necessidades específicas. Utilize fontes obrigatórias.*


## 6.5 Posicionamento

*a) Proposta de Valor Única (até 250 palavras)*
*Defina de maneira clara o que torna a sua aplicação única e valiosa para o mercado.*

*b) Estratégia de Diferenciação (até 250 palavras)*
*Explique como sua aplicação se destacará da concorrência, evidenciando a lógica por trás do posicionamento.*

## 6.6 Estratégia de Marketing

*a) Produto/Serviço (até 200 palavras)*
*Descreva as funcionalidades, benefícios e diferenciais da aplicação*

*6.2 Preço (até 200 palavras)*
*Explique o modelo de precificação adotado e justifique com base nas análises anteriores.*

*6.3 Praça (Distribuição) (até 200 palavras)*
*Apresente os canais digitais utilizados para distribuir e entregar a aplicação ao público.*

*6.4 Promoção (até 200 palavras)*
*Descreva as estratégias digitais planejadas, como SEO, redes sociais, marketing de conteúdo e campanhas pagas.*

# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

*Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral.*

*Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras*

*Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras*

# <a name="c8"></a>8. Referências (sprints 1 a 5)

_Incluir as principais referências de seu projeto, para que seu parceiro possa consultar caso ele se interessar em aprofundar. Um exemplo de referência de livro e de site:_<br>

LUCK, Heloisa. Liderança em gestão escolar. 4. ed. Petrópolis: Vozes, 2010. <br>
SOBRENOME, Nome. Título do livro: subtítulo do livro. Edição. Cidade de publicação: Nome da editora, Ano de publicação. <br>

INTELI. Adalove. Disponível em: https://adalove.inteli.edu.br/feed. Acesso em: 1 out. 2023 <br>
SOBRENOME, Nome. Título do site. Disponível em: link do site. Acesso em: Dia Mês Ano <br>

<a id="ref-5forcas"></a>MAGRETTA, Joan. Entendendo Michael Porter. Págs. 53 a 63. <br>

<a id="ref-iptQuemSomos"></a>IPT. IPT. Disponível em: https://ipt.br/quem-somos/. Acesso em: 29 Abril 2025 <br>

<a id="ref-matrizRisco"></a>Esfera Energia, blog.esfera. Disponível em: https://blog.esferaenergia.com.br/gestao-empresarial/matriz-de-risco. Acesso em: 29 Abril 2025 <br>

# <a name="c9"></a>Anexos


docs(2.1.5.)Matriz-de-Risco
*Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)*

 docs(1.)/Introducao
*Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)*

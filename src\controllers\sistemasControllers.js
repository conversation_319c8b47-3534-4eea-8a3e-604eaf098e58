// controllers/sistemasControllers.js

const sistemasModel = require('../models/sistemasModel');

const getAllSistema = async (req, res) => {
  try {
    const sistema = await sistemasModel.getAllSistema();
    res.status(200).json(sistema);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getSistemaById = async (req, res) => {
  try {
    const sistema = await sistemasModel.getSistemaById(req.params.id);
    if (sistema) {
      res.status(200).json(sistema);
    } else {
      res.status(404).json({ error: 'sistema não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createSistema = async (req, res) => {
  try {
    const {ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em} = req.body;
    const newSistema = await sistemasModel.createSistema(ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em);
    res.status(201).json(newSistema);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateSistema = async (req, res) => {
  try {
    const {ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em} = req.body;
    const updatedSistema = await sistemasModel.updateSistema(req.params.id,ambiente_id, tipo, descricao, criado_por, criado_em, atualizado_em);
    if (updatedSistema) {
      res.status(200).json(updatedSistema);
    } else {
      res.status(404).json({ error: 'Sistema não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteSistema = async (req, res) => {
  try {
    const deletedSistema = await sistemasModel.deleteSistema(req.params.id);
    if (deletedSistema) {
      res.status(200).json(deletedSistema);
    } else {
      res.status(404).json({ error: 'Sistema não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllSistema,
getSistemaById,
createSistema,
updateSistema,
deleteSistema

};

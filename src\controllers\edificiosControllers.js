// controllers/edificioControllers.js

const edificiosModel = require('../models/edificiosModel');

const getAllEdificios = async (req, res) => {
  try {
    const edificios = await edificiosModel.getAllEdificios();
    res.status(200).json(edificios);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getEdificioById = async (req, res) => {
  try {
    const edificio = await edificiosModel.getEdificioById(req.params.id);
    if (edificio) {
      res.status(200).json(edificio);
    } else {
      res.status(404).json({ error: 'Edificio não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createEdificios = async (req, res) => {
  try {
    const { inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em } = req.body;
    const newEdificio = await edificiosModel.createEdificios( inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em);
    res.status(201).json(newEdificio);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateEdificio = async (req, res) => {
  try {
    const { inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em} = req.body;
    const updatedEdificio = await userService.updateUser(req.params.id,  inspecao_id, andares, bloco, tipo_edificio, criado_por, criado_em, atualizado_em);
    if (updatedEdificio) {
      res.status(200).json(updatedEdificio);
    } else {
      res.status(404).json({ error: 'Edificio não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteEdificio = async (req, res) => {
  try {
    const deletedEdificio = await edificiosModel.deleteEdificio(req.params.id);
    if (deletedEdificio) {
      res.status(200).json(deletedEdificio);
    } else {
      res.status(404).json({ error: 'Edificio não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllEdificios,
getEdificioById,
createEdificios,
updateEdificio,
deleteEdificio,
};

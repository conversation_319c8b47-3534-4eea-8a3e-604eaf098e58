const express = require('express');
const router = express.Router();
const inspecoesController = require('../controllers/inspecoesController');

router.get('/', inspecoesController.getAllInspecoes);
router.get('/:id', inspecoesController.getInspecoesById);
router.post('/', inspecoesController.createInspecoes);
router.put('/:id', inspecoesController.updateInspecoes);
router.delete('/:id', inspecoesController.deleteInspecoes);

module.exports = router;
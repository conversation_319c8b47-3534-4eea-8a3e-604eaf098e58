<%# Cadastro page using standalone layout %>
<div class="layout-container flex h-full grow flex-col">
    <%# Simple header for cadastro page %>
    <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
        <div class="flex items-center gap-4 text-[#111418]">
            <h2 class="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
        </div>
    </header>
    <%# Main cadastro content %>
    <div class="px-40 flex flex-1 justify-center py-5">
        <div class="layout-content-container flex flex-col items-center w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1">
            <h2 class="text-[#111418] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">
                Criar conta
            </h2>

            <%# Registration form %>
            <form id="cadastroForm" class="flex flex-col items-center w-full">
                <%# Nome field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Nome</p>
                        <input placeholder="Nome" type="text" id="nome" name="nome" required
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="<%= typeof formData !== 'undefined' && formData.nome ? formData.nome : '' %>" />
                    </label>
                </div>

                <%# Sobrenome field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Sobrenome</p>
                        <input placeholder="Sobrenome" type="text" id="sobrenome" name="sobrenome" required
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="<%= typeof formData !== 'undefined' && formData.sobrenome ? formData.sobrenome : '' %>" />
                    </label>
                </div>

                <%# Email field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Email</p>
                        <input placeholder="Email" type="email" id="email" name="email" required
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="<%= typeof formData !== 'undefined' && formData.email ? formData.email : '' %>" />
                    </label>
                </div>

                <%# Senha field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Senha</p>
                        <input placeholder="Senha" type="password" id="senha" name="senha" required
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="" />
                    </label>
                </div>

                <%# Confirmar senha field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Confirmar senha</p>
                        <input placeholder="Confirmar senha" type="password" id="confirmarSenha" name="confirmarSenha" required
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="" />
                    </label>
                </div>

                <%# Submit button %>
                <div class="flex w-full max-w-[480px] px-4 py-3">
                    <button type="submit"
                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 flex-1 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                        <span class="truncate">Cadastrar</span>
                    </button>
                </div>

                <%# Error message %>
                <div id="errorMessage" class="mt-4 text-center text-red-500 text-sm">
                    <% if (typeof errorMessage !== 'undefined' && errorMessage) { %>
                        <%= errorMessage %>
                    <% } %>
                </div>

                <%# Login link %>
                <div class="mt-4 text-center px-4 pb-3 w-full max-w-[480px]">
                    <p class="text-sm text-[#637588] font-normal leading-normal">
                        Já tem uma conta?
                        <a href="/login" class="text-[#1980e6] font-medium underline">
                            Faça login
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<%# Registration form JavaScript %>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const cadastroForm = document.getElementById('cadastroForm');
        if (cadastroForm) {
            cadastroForm.addEventListener('submit', function (event) {
                event.preventDefault();

                const nome = document.getElementById('nome').value.trim();
                const sobrenome = document.getElementById('sobrenome').value.trim();
                const email = document.getElementById('email').value.trim();
                const senha = document.getElementById('senha').value.trim();
                const confirmarSenha = document.getElementById('confirmarSenha').value.trim();
                const errorMessageElement = document.getElementById('errorMessage');

                <%# Clear previous error messages %>
                errorMessageElement.textContent = '';

                <%# Basic validation %>
                if (!nome || !sobrenome || !email || !senha || !confirmarSenha) {
                    errorMessageElement.textContent = 'Por favor, preencha todos os campos.';
                    return;
                }

                if (senha !== confirmarSenha) {
                    errorMessageElement.textContent = 'As senhas não coincidem.';
                    return;
                }

                if (senha.length < 6) {
                    errorMessageElement.textContent = 'A senha deve ter pelo menos 6 caracteres.';
                    return;
                }

                <%# Demo success - redirect to login %>
                alert('Cadastro realizado com sucesso! Redirecionando para o login...');
                window.location.href = '/login';
            });
        }
    });
</script>
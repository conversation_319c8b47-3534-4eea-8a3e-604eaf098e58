const express = require('express');
const router = express.Router();
const ambientesControllers = require('../controllers/ambientesControllers');

router.get('/', ambientesControllers.getAllAmbiente);
router.get('/:id', ambientesControllers.getAmbienteById);
router.post('/', ambientesControllers.createAmbiente);
router.put('/:id', ambientesControllers.updateAmbiente);
router.delete('/:id', ambientesControllers.deleteAmbiente);

module.exports = router;

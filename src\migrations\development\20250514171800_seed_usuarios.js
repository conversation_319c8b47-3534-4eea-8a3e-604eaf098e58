const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async () => {
    const sqlPath = path.join(__dirname, 'seed_usuarios.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await db.query(`
      CREATE TABLE IF NOT EXISTS usuarios (
        id SERIAL PRIMARY KEY,
        nome VARCHAR(100),
        email VARCHAR(100),
        senha VARCHAR(100),
        criado_em TIMESTAMP
      );
    `);
    await db.query(sql);
    console.log('Dados inseridos em "usuarios" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS usuarios CASCADE;');
  },
};

const fs = require('fs');
const path = require('path');
const db = require('../../config/db');

module.exports = {
  up: async () => {
    const sqlPath = path.join(__dirname, 'seed_pavimentos.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');
    await db.query(`
      CREATE TABLE IF NOT EXISTS pavimentos(
        id SERIAL PRIMARY KEY,
        edificio_id INTEGER,
        andar VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (edificio_id) REFERENCES edificios(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
    await db.query(sql);
    console.log('Dados inseridos em "pavimentos" com sucesso.');
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS pavimentos CASCADE;');
  },
};

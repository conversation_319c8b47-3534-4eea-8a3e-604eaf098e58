const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS ambientes (
      id SERIAL PRIMARY KEY,
      pavimento_id INTEGER,
      nome VARCHAR(100),
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (pavimento_id) REFERENCES pavimentos(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "ambientes" criada com sucesso.');
};

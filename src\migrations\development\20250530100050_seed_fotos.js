const db = require('../../config/db');

module.exports = {
  up: async () => {
    await db.query(`
      CREATE TABLE IF NOT EXISTS fotos (
        id SERIAL PRIMARY KEY,
        patologia_id INTEGER,
        caminho_arquivo VARCHAR(300),
        legenda VARCHAR(300),
        criado_por INTEGER,
        criado_em TIMESTAMP,
        FOREIGN KEY (patologia_id) REFERENCES patologias(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS fotos CASCADE;');
  },
};

CREATE TABLE IF NOT EXISTS usuarios (
  id SERIAL PRIMARY KEY,
  nome VARCHAR(100),
  email VARCHAR(100),
  senha VARCHAR(100),
  criado_em TIMESTAMP
);

CREATE TABLE IF NOT EXISTS inspecoes (
  id SERIAL PRIMARY KEY,
  nome_edificio VARCHAR(200),
  endereco VARCHAR(300),
  tipo_edificio VARCHAR(50),
  torre_bloco VARCHAR(50),
  data_inicio DATE,
  data_fim DATE,
  status VARCHAR(50), -- nao_iniciado, em_andamento, concluido
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS relatorios (
  id SERIAL PRIMARY KEY,
  inspecao_id INTEGER,
  titulo VARCHAR(100),
  status VARCHAR(100), -- rascunho, final
  gerado_por INTEGER,
  gerado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
  FOREIGN KEY (gerado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS funcoes_usuarios (
  id SERIAL PRIMARY KEY,
  usuario_id INTEGER,
  funcao VARCHAR(50), -- inspetor, coordenador, administrador
  criado_em TIMESTAMP,
  FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS equipes_inspecao (
  id SERIAL PRIMARY KEY,
  inspecao_id INTEGER,
  usuario_id INTEGER,
  funcao VARCHAR(50), -- inspetor, coordenador, administrador
  criado_em TIMESTAMP,
  FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
  FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);
 
 --deb
CREATE TABLE IF NOT EXISTS edificios(
    id SERIAL PRIMARY KEY,
    inspecao_id INTEGER,
    numero VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
    Andares INTEGER,
    Bloco VARCHAR(50), -- Bloco A, Bloco B, etc
    tipo_edificio VARCHAR(50), -- residencial, comercial, misto
    criado_por INTEGER,
    criado_em TIMESTAMP,
    atualizado_em TIMESTAMP,
    FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
    FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS pavimentos(
    id SERIAL PRIMARY KEY,
    edificio_id INTEGER,
    andar VARCHAR(50), --Térreo, 1ºandar, 2ºandar, etc
    criado_por INTEGER,
    criado_em TIMESTAMP,
    atualizado_em TIMESTAMP,
    FOREIGN KEY (edificio_id) REFERENCES edificios(id),
    FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS ambientes (
  id SERIAL PRIMARY KEY,
  pavimento_id INTEGER,
  nome VARCHAR(100),
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (pavimento_id) REFERENCES pavimentos(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS sistemas (
  id SERIAL PRIMARY KEY,
  ambiente_id INTEGER,
  tipo VARCHAR(100), -- piso, parede, teto
  descricao TEXT,
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (ambiente_id) REFERENCES ambientes(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS patologias (
  id SERIAL PRIMARY KEY,
  sistema_id INTEGER,
  nome VARCHAR(100),
  descricao TEXT,
  criado_por INTEGER,
  criado_em TIMESTAMP,
  atualizado_em TIMESTAMP,
  FOREIGN KEY (sistema_id) REFERENCES sistemas(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

CREATE TABLE IF NOT EXISTS fotos (
  id SERIAL PRIMARY KEY,
  patologia_id INTEGER,
  caminho_arquivo VARCHAR(300),
  legenda VARCHAR(300),
  criado_por INTEGER,
  criado_em TIMESTAMP,
  FOREIGN KEY (patologia_id) REFERENCES patologias(id),
  FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);
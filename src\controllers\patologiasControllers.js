// controllers/sistemasControllers.js

const patologiasModel = require('../models/patologiasModel');

const getAllPatologia = async (req, res) => {
  try {
    const patologia = await patologiasModel.getAllPatologia();
    res.status(200).json(patologia);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getPatologiaById = async (req, res) => {
  try {
    const patologia = await patologiasModel.getPatologiaById(req.params.id);
    if (patologia) {
      res.status(200).json(patologia);
    } else {
      res.status(404).json({ error: 'patologia não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createPatologia = async (req, res) => {
  try {
    const { sistema_id, nome, descricao, criado_por, criado_em, atualizado_em} = req.body;
    const newPatologia = await patologiasModel.createPatologia( sistema_id, nome, descricao, criado_por, criado_em, atualizado_em);
    res.status(201).json(newPatologia);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updatePatologia = async (req, res) => {
  try {
    const { sistema_id, nome, descricao, criado_por, criado_em, atualizado_em} = req.body;
    const updatedPatologia = await patologiasModel.updatePatologia(req.params.id, sistema_id, nome, descricao, criado_por, criado_em, atualizado_em);
    if (updatedPatologia) {
      res.status(200).json(updatedPatologia);
    } else {
      res.status(404).json({ error: 'Patologia não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deletePatologia = async (req, res) => {
  try {
    const deletedPatologia = await patologiasModel.deletePatologia(req.params.id);
    if (deletedPatologia) {
      res.status(200).json(deletedPatologia);
    } else {
      res.status(404).json({ error: 'Patologia não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllPatologia,
getPatologiaById,
createPatologia,
updatePatologia,
deletePatologia

};

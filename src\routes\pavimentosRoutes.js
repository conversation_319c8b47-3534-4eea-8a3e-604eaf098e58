const express = require('express');
const router = express.Router();
const pavimentosControllers = require('../controllers/pavimentosControllers');

router.get('/', pavimentosControllers.getAllPavimento);
router.get('/:id', pavimentosControllers.getPavimentoById);
router.post('/', pavimentosControllers.createPavimento);
router.put('/:id', pavimentosControllers.updatePavimento);
router.delete('/:id', pavimentosControllers.deletePavimento);

module.exports = router;

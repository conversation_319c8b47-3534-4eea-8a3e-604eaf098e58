<%# Login page using standalone layout %>
<div class="layout-container flex h-full grow flex-col">
    <%# Simple header for login page %>
    <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
        <div class="flex items-center gap-4 text-[#111418]">
            <h2 class="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
        </div>
    </header>
    <%# Main login content %>
    <div class="px-40 flex flex-1 justify-center py-5">
        <div class="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1">
            <h2 class="text-[#111418] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">
                Login
            </h2>

            <%# Login form %>
            <form id="loginForm" class="flex flex-col items-center w-full">
                <%# Email field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Email</p>
                        <input placeholder="<EMAIL>" type="email" id="email" name="email"
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="<%= typeof formData !== 'undefined' && formData.email ? formData.email : '' %>" />
                    </label>
                </div>

                <%# Password field %>
                <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                    <label class="flex flex-col min-w-40 flex-1">
                        <p class="text-[#111418] text-base font-medium leading-normal pb-2">Senha</p>
                        <input placeholder="Senha" type="password" id="senha" name="senha"
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                            value="" />
                    </label>
                </div>

                <%# Submit button %>
                <div class="flex w-full max-w-[480px] px-4 py-3">
                    <button type="submit"
                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 flex-1 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                        <span class="truncate">Entrar</span>
                    </button>
                </div>

                <%# Error message %>
                <div id="errorMessage" class="mt-4 text-center text-red-500 text-sm">
                    <% if (typeof errorMessage !== 'undefined' && errorMessage) { %>
                        <%= errorMessage %>
                    <% } %>
                </div>

                <%# Forgot password link %>
                <div class="mt-6 text-center px-4 w-full max-w-[480px]">
                    <a href="/recuperar-senha"
                        class="text-[#637588] text-sm font-normal leading-normal underline">
                        Problemas para entrar?
                    </a>
                </div>

                <%# Sign up link %>
                <div class="mt-4 text-center px-4 pb-3 w-full max-w-[480px]">
                    <p class="text-sm text-[#637588] font-normal leading-normal">
                        Não tem uma conta?
                        <a href="/cadastro" class="text-[#1572cf] font-medium underline">
                            Crie seu Cadastro
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<%# Login form JavaScript %>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', function (event) {
                event.preventDefault();
                const emailInput = document.getElementById('email');
                const passwordInput = document.getElementById('senha');
                const errorMessageElement = document.getElementById('errorMessage');

                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();

                <%# Clear previous error messages %>
                errorMessageElement.textContent = '';

                <%# Basic validation %>
                if (!email || !password) {
                    errorMessageElement.textContent = 'Por favor, preencha todos os campos.';
                    return;
                }

                <%# Demo authentication logic %>
                if (email === '<EMAIL>' && password === '123456') {
                    window.location.href = '/admin/dashboard';
                } else if (email === '<EMAIL>' && password === '123456') {
                    window.location.href = '/inspetor/lista-inspecoes';
                } else {
                    errorMessageElement.textContent = 'Email ou senha inválidos.';
                }
            });
        }
    });
</script>
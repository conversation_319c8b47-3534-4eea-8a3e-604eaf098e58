require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Lê o argumento passado na linha de comando (ex: "development" ou "production")
const envArg = process.argv[2] || 'development';
const folder = path.join(__dirname, envArg);

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: envArg === 'production' ? { rejectUnauthorized: false } : false,
});

(async () => {
  try {
    const files = fs.readdirSync(folder)
      .filter(file => file.endsWith('.js'))
      .sort(); // ordena pelo timestamp

    for (const file of files) {
      const migration = require(path.join(folder, file));
      console.log(file)
      await migration(pool);
    }

    console.log(`Migrações de "${envArg}" executadas com sucesso.`);
    await pool.end();
  } catch (err) {
    console.error('Erro ao executar migrações:', err);
    process.exit(1);
  }
})();

<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Criar Nova Inspeção</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/dashboard">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="/dashboard"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="/relatorios"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="/membros"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('/images/perfil.png');">
                    </div>
                </div>
            </header>
            <main class="flex-1">
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                        <div class="flex flex-wrap justify-between items-center gap-3 p-4">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">
                                Criar Nova Inspeção</p>
                        </div>
                        <div class="flex flex-col gap-4 p-4">
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-name"
                                    class="text-[#111518] text-sm font-medium leading-normal">Nome da Inspeção</label>
                                <input id="inspection-name" placeholder="Ex: Inspeção Edifício Principal" type="text"
                                    class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="flex flex-col gap-1.5 md:col-span-2">
                                    <label for="inspection-address"
                                        class="text-[#111518] text-sm font-medium leading-normal">Endereço</label>
                                    <input id="inspection-address" placeholder="Ex: Rua das Palmeiras" type="text"
                                        class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <label for="inspection-number"
                                        class="text-[#111518] text-sm font-medium leading-normal">Número</label>
                                    <input id="inspection-number" placeholder="Ex: 123" type="text"
                                        class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                                </div>
                            </div>
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-complement"
                                    class="text-[#111518] text-sm font-medium leading-normal">Complemento</label>
                                <input id="inspection-complement" placeholder="Ex: Bloco A, Apto 101" type="text"
                                    class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                            </div>
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-description"
                                    class="text-[#111518] text-sm font-medium leading-normal">Descrição</label>
                                <textarea id="inspection-description" placeholder="Descreva os detalhes da inspeção..."
                                    rows="4"
                                    class="flex items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10"></textarea>
                            </div>
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-date"
                                    class="text-[#111518] text-sm font-medium leading-normal">Data</label>
                                <input id="inspection-date" type="date"
                                    class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                            </div>
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-responsible"
                                    class="text-[#111518] text-sm font-medium leading-normal">Coordenador
                                    Responsável</label>
                                <input id="inspection-responsible" placeholder="Ex: João Silva" type="text"
                                    class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                            </div>
                            <div class="flex flex-col gap-1.5">
                                <label for="inspection-type"
                                    class="text-[#111518] text-sm font-medium leading-normal">Tipo</label>
                                <input id="inspection-type" placeholder="Ex: Inspeção de Segurança" type="text"
                                    class="flex h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] placeholder:text-[#637688] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10" />
                            </div>

                            <div class="flex flex-col gap-3 pt-4 border-t border-gray-200 mt-4" x-data="inspectionForm">
                                <h3 class="text-[#111518] text-lg font-semibold leading-tight">Designar Inspetores</h3>
                                <div class="flex flex-col gap-1.5">
                                    <label for="inspector-select"
                                        class="text-[#111518] text-sm font-medium leading-normal">Selecionar
                                        Inspetor</label>
                                    <div class="flex gap-2">
                                        <select id="inspector-select" x-model="selectedInspector"
                                            class="flex-grow h-10 items-center rounded-lg border border-[#dee0e3] bg-white px-3 py-2 text-sm text-[#111518] focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/10">
                                            <option value="" disabled selected>Selecione um inspetor</option>
                                            <template x-for="inspector in availableInspectors" :key="inspector.value">
                                                <option :value="inspector.value" x-text="inspector.name"></option>
                                            </template>
                                        </select>
                                        <button type="button" @click="addInspector"
                                            class="flex min-w-[84px] items-center justify-center rounded-lg h-10 px-4 bg-blue-600 text-white text-sm font-medium leading-normal transition-colors hover:bg-blue-700">
                                            <span class="truncate">Adicionar</span>
                                        </button>
                                    </div>
                                </div>
                                <div id="assigned-inspectors" class="mt-2">
                                    <p class="text-sm text-gray-500" x-show="!inspectors.length">Nenhum inspetor
                                        designado ainda.</p>
                                    <template x-for="inspector in inspectors" :key="inspector.value">
                                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-md mt-2">
                                            <span x-text="inspector.name"></span>
                                            <button type="button" @click="removeInspector(inspector.value)"
                                                class="text-red-500 hover:text-red-700">Remover</button>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end gap-3 p-4">
                            <a href="/dashboard"
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal transition-colors hover:bg-gray-200">
                                <span class="truncate">Cancelar</span>
                            </a>
                            <a href="/dashboard"
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#007bff] text-white text-sm font-medium leading-normal transition-colors hover:bg-blue-700">
                                <span class="truncate">Salvar Inspeção</span>
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script>
        const inspectionsData = {
            "INSP-001": {
                id: "INSP-001",
                creationDate: "01/03/2024",
                scheduledDate: "15/03/2024",
                status: "Agendada",
                engineer: "Carlos Silva",
                client: "Edifício Central",
                address: "Rua Principal, 123, Centro",
                type: "Estrutural",
                progress: 0,
                details: "Inspeção inicial da estrutura do edifício.",
                contactName: "Ana Paula",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 98765-4321",
                ambientes: [
                    {
                        id: "AMB-001",
                        nome: "Hall de Entrada",
                        observacoes: "Verificar revestimentos e iluminação.",
                        sistemas: [
                            {
                                id: "SIS-001",
                                nome: "Sistema Elétrico",
                                elementos: [
                                    { id: "ELE-001", nome: "Quadro de Disjuntores", observacoes: "Verificar conexões e identificação dos circuitos.", patologias: [] },
                                    { id: "ELE-002", nome: "Luminárias", observacoes: "Checar funcionamento e estado de conservação.", patologias: [] }
                                ]
                            },
                            {
                                id: "SIS-002",
                                nome: "Sistema Hidráulico",
                                elementos: [
                                    { id: "ELE-003", nome: "Torneiras", observacoes: "Verificar vazamentos e funcionamento.", patologias: [] }
                                ]
                            }
                        ]
                    }
                ]
            },
            "INSP-002": {
                id: "INSP-002",
                creationDate: "10/03/2024",
                scheduledDate: "25/03/2024",
                status: "Em Andamento",
                engineer: "Fernanda Lima",
                client: "Condomínio Residencial Flores",
                address: "Avenida das Palmeiras, 456, Bairro Sul",
                type: "Hidráulica",
                progress: 50,
                details: "Inspeção completa do sistema hidráulico.",
                contactName: "Pedro Costa",
                contactEmail: "<EMAIL>",
                contactPhone: "(21) 91234-5678",
                ambientes: [
                    {
                        id: "AMB-002",
                        nome: "Banheiro Social",
                        observacoes: "Atenção a vazamentos e infiltrações.",
                        sistemas: [
                            {
                                id: "SIS-003",
                                nome: "Sistema Hidráulico",
                                elementos: [
                                    { id: "ELE-004", nome: "Vaso Sanitário", observacoes: "Verificar vedação e caixa acoplada.", patologias: [{ id: "PAT-001", descricao: "Vazamento na base", foto: "url_foto_vazamento.jpg", observacoes_patologia: "Pequeno gotejamento contínuo." }] },
                                    { id: "ELE-005", nome: "Chuveiro", observacoes: "Checar pressão da água e estado do registro.", patologias: [] }
                                ]
                            }
                        ]
                    }
                ]
            },
            "INSP-003": {
                id: "INSP-003",
                creationDate: "20/03/2024",
                scheduledDate: "05/04/2024",
                status: "Concluída",
                engineer: "Roberto Souza",
                client: "Centro Comercial Plaza",
                address: "Rua do Comércio, 789, Centro",
                type: "Elétrica",
                progress: 100,
                details: "Revisão completa do sistema elétrico das áreas comuns.",
                contactName: "Mariana Oliveira",
                contactEmail: "<EMAIL>",
                contactPhone: "(31) 99999-8888",
                ambientes: [
                    {
                        id: "AMB-003",
                        nome: "Corredor Principal",
                        observacoes: "Verificar iluminação de emergência e sinalização.",
                        sistemas: [
                            {
                                id: "SIS-004",
                                nome: "Sistema Elétrico",
                                elementos: [
                                    { id: "ELE-006", nome: "Luzes de Emergência", observacoes: "Testar acionamento automático.", patologias: [] },
                                    { id: "ELE-007", nome: "Tomadas", observacoes: "Verificar aterramento e conservação.", patologias: [{ id: "PAT-002", descricao: "Tomada com espelho quebrado", foto: "url_foto_tomada.jpg", observacoes_patologia: "Risco de choque elétrico." }] }
                                ]
                            }
                        ]
                    }
                ]
            }
        };

        document.addEventListener('alpine:init', () => {
            Alpine.data('inspectionForm', () => ({
                inspectors: [],
                availableInspectors: [
                    { value: 'Carlos Silva', name: 'Carlos Silva' },
                    { value: 'Fernanda Lima', name: 'Fernanda Lima' },
                    { value: 'Roberto Souza', name: 'Roberto Souza' }
                ],
                selectedInspector: '',
                addInspector() {
                    if (this.selectedInspector && !this.inspectors.find(i => i.value === this.selectedInspector)) {
                        const inspector = this.availableInspectors.find(i => i.value === this.selectedInspector);
                        if (inspector) {
                            this.inspectors.push(inspector);
                        }
                    }
                    this.selectedInspector = '';
                },
                removeInspector(inspectorValue) {
                    this.inspectors = this.inspectors.filter(i => i.value !== inspectorValue);
                }
            }));
        });
    </script>
</body>

</html>
const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS inspecoes (
      id SERIAL PRIMARY KEY,
      nome_edificio VARCHAR(200),
      endereco VARCHAR(300),
      tipo_edificio VARCHAR(50),
      torre_bloco VARCHAR(50),
      data_inicio DATE,
      data_fim DATE,
      status VARCHAR(50), -- nao_iniciado, em_andamento, concluido
      criado_por INTEGER,
      criado_em TIMESTAMP,
      atualizado_em TIMESTAMP,
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "inspecoes" criada com sucesso.');
};
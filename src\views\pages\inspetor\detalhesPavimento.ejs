<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Pavimento - Inspetor</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/listaInspecoes">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                  <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/listaInspecoes">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/relatorios">Relatórios</a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>                    <a href="/perfil" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('/images/perfil.png');">
                    </a>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="floorDetails()">
                    <!-- Cabeçalho do Pavimento -->
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">
                                <span x-text="floor.name"></span>
                            </p>
                            <p class="text-[#637588] text-sm font-normal leading-normal">
                                ID: <span x-text="floor.id"></span> | Status: 
                                <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                      :class="getStatusColor(floor.status)" 
                                      x-text="floor.status"></span>
                            </p>
                        </div>
                        <div class="flex gap-3">
                            <button @click="toggleEditMode()" 
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span x-text="editMode ? 'Salvar' : 'Editar'"></span>
                            </button>
                        </div>
                    </div>

                    <!-- Navegação Breadcrumb -->
                    <div class="px-4 py-2 text-sm text-gray-600">
                        <a href="/detalhesInspecao" class="text-blue-600 hover:text-blue-800" x-text="`Inspeção ${inspectionId}`"></a>
                        <span class="mx-2">></span>
                        <a href="/detalhesEdificio" class="text-blue-600 hover:text-blue-800" x-text="`Edifício ${buildingId}`"></a>
                        <span class="mx-2">></span>
                        <span x-text="floor.name"></span>
                    </div>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Nome do Pavimento</p>
                            <input x-model="floor.name" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="text">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Nível</p>
                            <input x-model="floor.level" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="number">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Altura do Pé-direito (m)</p>
                            <input x-model="floor.height" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="number" step="0.1">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Área Total (m²)</p>
                            <input x-model="floor.area" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="number" step="0.1">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Uso Principal</p>
                            <select x-model="floor.usage" 
                                    :disabled="!editMode"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed">
                                <option value="Áreas Comuns">Áreas Comuns</option>
                                <option value="Escritórios">Escritórios</option>
                                <option value="Comercial">Comercial</option>
                                <option value="Residencial">Residencial</option>
                                <option value="Técnico">Técnico</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Capacidade (pessoas)</p>
                            <input x-model="floor.capacity" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="number">
                        </div>
                    </div>

                    <!-- Descrição -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Descrição</p>
                        <textarea x-model="floor.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                  rows="3"
                                  placeholder="Descrição do pavimento..."></textarea>
                    </div>

                    <!-- Observações -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Observações</p>
                        <textarea x-model="floor.observations" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                  rows="3"
                                  placeholder="Observações específicas sobre o pavimento..."></textarea>
                    </div>

                    <!-- Fotos do Pavimento -->
                    <div class="px-4 py-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight">Fotos do Pavimento</h3>
                            <button x-show="editMode" @click="addPhoto()" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Adicionar Foto
                            </button>
                        </div>
                        <div class="grid grid-cols-3 gap-4" x-show="floor.photos && floor.photos.length > 0">
                            <template x-for="(photo, index) in floor.photos" :key="index">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description" 
                                         class="w-full h-32 object-cover rounded-lg cursor-pointer"
                                         @click="viewPhoto(photo)">
                                    <div x-show="editMode" class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <button @click="removePhoto(index)" 
                                                class="bg-red-500 text-white rounded-full p-1 hover:bg-red-600">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-2 text-sm text-gray-600" x-text="photo.description"></p>
                                </div>
                            </template>
                        </div>
                        <div x-show="!floor.photos || floor.photos.length === 0" 
                             class="text-center py-8 text-gray-500">
                            Nenhuma foto adicionada ainda
                        </div>
                    </div>

                    <!-- Lista de Ambientes -->
                    <div class="px-4 py-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight">Ambientes</h3>
                            <button x-show="editMode" @click="addEnvironment()" 
                                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                Adicionar Ambiente
                            </button>
                        </div>
                        <div class="space-y-3">
                            <template x-for="environment in floor.environments" :key="environment.id">
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-center">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900" x-text="environment.name"></h4>
                                            <p class="text-sm text-gray-600" x-text="environment.description"></p>
                                            <div class="mt-2 flex gap-4 text-xs text-gray-500">
                                                <span>Tipo: <span x-text="environment.type"></span></span>
                                                <span>Área: <span x-text="environment.area"></span> m²</span>
                                                <span>Status: 
                                                    <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                                          :class="getStatusColor(environment.status)" 
                                                          x-text="environment.status"></span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex gap-2">
                                            <button @click="viewEnvironmentDetails(environment.id)" 
                                                    class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                                                Ver Detalhes
                                            </button>
                                            <button x-show="editMode" @click="removeEnvironment(environment.id)" 
                                                    class="px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200">
                                                Remover
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!floor.environments || floor.environments.length === 0" 
                             class="text-center py-8 text-gray-500">
                            Nenhum ambiente cadastrado ainda
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualizar/editar fotos -->
    <div x-show="showPhotoModal" @click.away="closePhotoModal()" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">Foto</h3>
                <button @click="closePhotoModal()" class="text-gray-500 hover:text-gray-700">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div x-show="currentPhoto">
                <img :src="currentPhoto?.url" :alt="currentPhoto?.description" 
                     class="w-full max-h-96 object-contain mb-4">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium mb-1">Descrição</label>
                        <textarea x-model="currentPhoto.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                                  rows="3"></textarea>
                    </div>
                    <div x-show="editMode" class="flex gap-3">
                        <button @click="savePhotoDescription()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Salvar
                        </button>
                        <button @click="closePhotoModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function floorDetails() {
            return {
                editMode: false,
                showPhotoModal: false,
                currentPhoto: null,
                inspectionId: '',
                buildingId: '',
                floor: {
                    id: '',
                    name: '',
                    level: 0,
                    height: 0,
                    area: 0,
                    usage: '',
                    capacity: 0,
                    status: '',
                    description: '',
                    observations: '',
                    photos: [],
                    environments: []
                },

                init() {
                    this.loadFloor();
                },                loadFloor() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const floorId = urlParams.get('id');
                    this.buildingId = urlParams.get('building');
                    this.inspectionId = urlParams.get('inspection');
                    
                    // Dados sincronizados
                    const floorsData = {
                        'PAV-001': {
                            id: 'PAV-001',
                            name: 'Térreo',
                            level: 0,
                            height: 3.2,
                            area: 1200.0,
                            usage: 'Circulação e Comercial',
                            capacity: 200,
                            status: 'Ativo',
                            description: 'Hall de entrada, recepção e área comercial. Pavimento principal com acesso direto à rua.',
                            observations: 'Verificar estado das juntas do piso de mármore no hall principal.',
                            photos: [
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Vista geral do hall de entrada'
                                },
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Área de recepção'
                                },
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Área comercial'
                                }
                            ],
                            environments: [
                                {
                                    id: 'AMB-001',
                                    name: 'Hall de Entrada',
                                    type: 'Circulação',
                                    area: 120.0,
                                    description: 'Hall principal de entrada do edifício',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-002',
                                    name: 'Recepção',
                                    type: 'Atendimento',
                                    area: 85.0,
                                    description: 'Área de recepção e atendimento',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-003',
                                    name: 'Loja 1',
                                    type: 'Comercial',
                                    area: 150.0,
                                    description: 'Espaço comercial principal',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-004',
                                    name: 'Loja 2',
                                    type: 'Comercial',
                                    area: 180.0,
                                    description: 'Segundo espaço comercial',
                                    status: 'Ativo'
                                }
                            ]
                        },
                        'PAV-002': {
                            id: 'PAV-002',
                            name: 'Mezanino',
                            level: 1,
                            height: 2.8,
                            area: 800.0,
                            usage: 'Escritórios',
                            capacity: 60,
                            status: 'Ativo',
                            description: 'Área administrativa e salas de reunião. Espaço corporativo moderno.',
                            observations: 'Piso elevado em bom estado, sistema de ar condicionado funcionando adequadamente.',
                            photos: [
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Área administrativa'
                                },
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Sala de reuniões'
                                }
                            ],
                            environments: [
                                {
                                    id: 'AMB-005',
                                    name: 'Sala Administrativa',
                                    type: 'Escritório',
                                    area: 200.0,
                                    description: 'Área administrativa principal',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-006',
                                    name: 'Sala de Reunião A',
                                    type: 'Reunião',
                                    area: 50.0,
                                    description: 'Sala de reunião pequena',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-007',
                                    name: 'Sala de Reunião B',
                                    type: 'Reunião',
                                    area: 60.0,
                                    description: 'Sala de reunião média',
                                    status: 'Ativo'
                                }
                            ]
                        },
                        'PAV-004': {
                            id: 'PAV-004',
                            name: 'Térreo',
                            level: 0,
                            height: 4.0,
                            area: 2500.0,
                            usage: 'Comercial',
                            capacity: 500,
                            status: 'Ativo',
                            description: 'Lojas principais e praça central. Área de maior circulação do shopping.',
                            observations: 'Alto fluxo de pessoas, manter atenção especial aos sistemas de segurança.',
                            photos: [
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Praça central'
                                },
                                {
                                    url: '../ícones/apartamento.png',
                                    description: 'Corredor de lojas'
                                }
                            ],
                            environments: [
                                {
                                    id: 'AMB-008',
                                    name: 'Praça Central',
                                    type: 'Circulação',
                                    area: 400.0,
                                    description: 'Área central de circulação',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-009',
                                    name: 'Loja Âncora',
                                    type: 'Comercial',
                                    area: 800.0,
                                    description: 'Loja principal do shopping',
                                    status: 'Ativo'
                                },
                                {
                                    id: 'AMB-010',
                                    name: 'Corredor Principal',
                                    type: 'Circulação',
                                    area: 300.0,
                                    description: 'Corredor principal de circulação',
                                    status: 'Ativo'
                                }
                            ]
                        }
                    };

                    this.floor = floorsData[floorId] || floorsData['PAV-001'];
                },

                toggleEditMode() {
                    if (this.editMode) {
                        this.saveFloor();
                    }
                    this.editMode = !this.editMode;
                },

                saveFloor() {
                    console.log('Salvando pavimento:', this.floor);
                    alert('Pavimento salvo com sucesso!');
                },                getStatusColor(status) {
                    const colors = {
                        'Ativo': 'bg-green-100 text-green-800',
                        'Em Andamento': 'bg-blue-100 text-blue-800',
                        'Em Manutenção': 'bg-yellow-100 text-yellow-800',
                        'Inativo': 'bg-red-100 text-red-800'
                    };
                    return colors[status] || 'bg-gray-100 text-gray-800';
                },

                addPhoto() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            const url = URL.createObjectURL(file);
                            this.floor.photos.push({
                                url: url,
                                description: `Nova foto - ${new Date().toLocaleString()}`
                            });
                        }
                    };
                    input.click();
                },

                removePhoto(index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.floor.photos.splice(index, 1);
                    }
                },

                viewPhoto(photo) {
                    this.currentPhoto = { ...photo };
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.currentPhoto = null;
                },

                savePhotoDescription() {
                    const index = this.floor.photos.findIndex(p => p.url === this.currentPhoto.url);
                    if (index !== -1) {
                        this.floor.photos[index].description = this.currentPhoto.description;
                    }
                    this.closePhotoModal();
                },

                addEnvironment() {
                    const newEnvironment = {
                        id: `ENV-${Date.now()}`,
                        name: 'Novo Ambiente',
                        type: 'Comum',
                        area: 0,
                        description: 'Descrição do ambiente',
                        status: 'Pendente'
                    };
                    this.floor.environments.push(newEnvironment);
                },

                removeEnvironment(environmentId) {
                    if (confirm('Tem certeza que deseja remover este ambiente?')) {
                        this.floor.environments = this.floor.environments.filter(e => e.id !== environmentId);
                    }
                },

                viewEnvironmentDetails(environmentId) {
                    window.location.href = `detalhesAmbiente.html?id=${environmentId}&floor=${this.floor.id}&building=${this.buildingId}&inspection=${this.inspectionId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const buildingId = urlParams.get('building');
            const inspectionId = urlParams.get('inspection');
            
            if (buildingId && inspectionId) {
                window.location.href = `detalhesEdificio.html?id=${buildingId}&inspection=${inspectionId}`;
            } else {
                window.location.href = '/listaInspecoes';
            }
        }
    </script>
</body>

</html>
const db = require('../config/db');

class inspecoes {
  static async getAll() {
    const result = await db.query('SELECT * FROM inspecoes');
    return result.rows;
  }

  static async getById(id) {
    const result = await db.query('SELECT * FROM inspecoes WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(data) {
    const result = await db.query(
      'INSERT INTO inspecoes (/* campos_aqui */) VALUES ($1, $2, $3, $4) RETURNING *',
      [/* data.campo1, data.campo2, ... */]
    );
    return result.rows[0];
  }

  static async update(id, data) {
    const result = await db.query(
      'UPDATE inspecoes SET /* campo1 = $1, campo2 = $2, ... */ WHERE id = $n RETURNING *',
      [/* data.campo1, data.campo2, ..., id */]
    );
    return result.rows[0];
  }

  static async delete(id) {
    const result = await db.query('DELETE FROM inspecoes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = inspecoes;

const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS equipes_inspecao (
      id SERIAL PRIMARY KEY,
      inspecao_id INTEGER,
      usuario_id INTEGER,
      funcao VARCHAR(50), -- inspetor, coordenador, administrador
      criado_em TIMESTAMP,
      FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
      FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "equipes_inspecao" criada com sucesso.');
};

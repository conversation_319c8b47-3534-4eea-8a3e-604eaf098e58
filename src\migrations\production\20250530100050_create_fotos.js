const fs = require('fs');
const path = require('path');

module.exports = async (pool) => {
  const sql = `
    CREATE TABLE IF NOT EXISTS fotos (
      id SERIAL PRIMARY KEY,
      patologia_id INTEGER,
      caminho_arquivo VARCHAR(300),
      legenda VARCHAR(300),
      criado_por INTEGER,
      criado_em TIMESTAMP,
      FOREIGN KEY (patologia_id) REFERENCES patologias(id),
      FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );
  `;
  await pool.query(sql);
  console.log('Tabela "fotos" criada com sucesso.');
};

// controllers/sistemasControllers.js

const fotosModel = require('../models/fotosModel');

const getAllFoto = async (req, res) => {
  try {
    const foto = await fotosModel.getAllFoto();
    res.status(200).json(foto);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getFotoById = async (req, res) => {
  try {
    const foto = await fotosModel.getFotoById(req.params.id);
    if (foto) {
      res.status(200).json(foto);
    } else {
      res.status(404).json({ error: 'foto não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const createFoto = async (req, res) => {
  try {
    const {patologia_id, caminho_arquivo, legenda, criado_por, criado_em} = req.body;
    const newFoto = await fotosModel.createFoto(patologia_id, caminho_arquivo, legenda, criado_por, criado_em);
    res.status(201).json(newFoto);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const updateFoto = async (req, res) => {
  try {
    const {patologia_id, caminho_arquivo, legenda, criado_por, criado_em} = req.body;
    const updatedFoto = await fotosModel.createFoto(patologia_id, caminho_arquivo, legenda, criado_por, criado_em);
    if (updatedFoto) {
      res.status(200).json(updatedFoto);
    } else {
      res.status(404).json({ error: 'Foto não encontrado' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteFoto = async (req, res) => {
  try {
    const deletedFoto = await fotosModel.deleteFoto(req.params.id);
    if (deletedFoto) {
      res.status(200).json(deletedFoto);
    } else {
      res.status(404).json({ error: 'Foto não encontrada' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
getAllFoto,
getFotoById,
createFoto,
updateFoto,
deleteFoto

};

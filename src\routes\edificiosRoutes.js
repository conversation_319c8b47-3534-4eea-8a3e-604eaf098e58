const express = require('express');
const router = express.Router();
const edificiosControllers = require('../controllers/edificiosControllers');

router.get('/', edificiosControllers.getAllEdificios);
router.get('/:id', edificiosControllers.getEdificioById);
router.post('/', edificiosControllers.createEdificios);
router.put('/:id', edificiosControllers.updateEdificio);
router.delete('/:id', edificiosControllers.deleteEdificio);

module.exports = router;

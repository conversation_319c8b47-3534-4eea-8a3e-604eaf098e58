<%# Dynamic header component based on user role %>
<header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
  <%# Logo and brand %>
  <div class="flex items-center gap-4 text-[#111518]">
    <% if (userRole === 'admin') { %>
      <a href="/admin/dashboard">
        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
      </a>
    <% } else if (userRole === 'inspetor') { %>
      <a href="/inspetor/lista-inspecoes">
        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
      </a>
    <% } else { %>
      <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
    <% } %>
  </div>

  <%# Navigation menu - only show for authenticated users %>
  <% if (userRole === 'admin' || userRole === 'inspetor') { %>
    <nav class="flex flex-1 justify-center">
      <div class="flex flex-row gap-x-6 items-center">
        <% if (userRole === 'admin') { %>
          <%# Admin navigation %>
          <a href="/admin/dashboard"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'dashboard' ? 'nav-item-active' : '' %>">
            <img src="/images/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
            Painel
          </a>
          <a href="/admin/relatorios"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'relatorios' ? 'nav-item-active' : '' %>">
            <img src="/images/quote-request.png" alt="Relatórios" width="20px" height="20px">
            Relatórios
          </a>
          <a href="/admin/membros"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'membros' ? 'nav-item-active' : '' %>">
            <img src="/images/grupo.png" alt="Membros" width="20px" height="20px">
            Membros
          </a>
        <% } else if (userRole === 'inspetor') { %>
          <%# Inspector navigation %>
          <a href="/inspetor/lista-inspecoes"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'lista-inspecoes' ? 'nav-item-active' : '' %>">
            <img src="/images/lista.png" alt="Inspeções" width="20px" height="20px">
            Inspeções
          </a>
          <a href="/inspetor/relatorios"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'relatorios' ? 'nav-item-active' : '' %>">
            <img src="/images/quote-request.png" alt="Relatórios" width="20px" height="20px">
            Relatórios
          </a>
          <a href="/inspetor/perfil"
            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center <%= currentPage === 'perfil' ? 'nav-item-active' : '' %>">
            <img src="/images/perfil.png" alt="Perfil" width="20px" height="20px">
            Perfil
          </a>
        <% } %>
      </div>
    </nav>

    <%# User actions and notifications %>
    <div class="flex justify-end gap-8">
      <%# Notifications dropdown %>
      <div class="flex items-center gap-4"
        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
        <button @click="toggleNotifications()" class="relative">
          <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z"></path>
            </svg>
          </div>
          <div x-show="hasNotifications" class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
        </button>

        <%# Notifications dropdown content %>
        <div x-show="notificationsOpen" @click.away="closeNotifications()"
          class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
          <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
          <div class="border-t border-gray-200"></div>
          <div class="max-h-64 overflow-y-auto">
            <% if (userRole === 'admin') { %>
              <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                Inspeção INSP-001 atribuída à equipe de inspetores.
              </a>
              <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                Relatório da inspeção INSP-003 concluído.
              </a>
            <% } else { %>
              <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                Nova inspeção atribuída: INSP-001
              </a>
              <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                Prazo da inspeção INSP-002 se aproxima
              </a>
            <% } %>
            <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
              Nenhuma nova notificação.
            </div>
          </div>
        </div>
      </div>

      <%# User profile picture %>
      <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
        style="background-image: url('/images/perfil.png');">
      </div>
    </div>
  <% } %>
</header>
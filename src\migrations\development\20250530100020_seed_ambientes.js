const db = require('../../config/db');

module.exports = {
  up: async () => {
    await db.query(`
      CREATE TABLE IF NOT EXISTS ambientes (
        id SERIAL PRIMARY KEY,
        pavimento_id INTEGER,
        nome VARCHAR(100),
        criado_por INTEGER,
        criado_em TIMESTAMP,
        atualizado_em TIMESTAMP,
        FOREIGN KEY (pavimento_id) REFERENCES pavimentos(id),
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
      );
    `);
  },
  down: async () => {
    await db.query('DROP TABLE IF EXISTS ambientes CASCADE;');
  },
};

const express = require('express');
const router = express.Router();
const patologiasControllers = require('../controllers/patologiasControllers');

router.get('/', patologiasControllers.getAllPatologia);
router.get('/:id', patologiasControllers.getPatologiaById);
router.post('/', patologiasControllers.createPatologia);
router.put('/:id', patologiasControllers.updatePatologia);
router.delete('/:id', patologiasControllers.deletePatologia);

module.exports = router;
